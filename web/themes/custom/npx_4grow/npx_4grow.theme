<?php

use Dr<PERSON>al\commerce_product\Entity\Product;
use <PERSON><PERSON>al\npx_main\NpxDateUtils;
use <PERSON><PERSON>al\npx_training\TrainingDateFields;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Dr<PERSON>al\paragraphs\Entity\Paragraph;
use <PERSON><PERSON>al\Core\StringTranslation\TranslatableMarkup;
use Drupal\Core\Url;
use Dr<PERSON>al\views\ViewExecutable;

/**
 * @file
 * Functions to support theming in the npx_4grow theme.
 */

/**
 * Implements hook_preprocess_HOOK() for html.html.twig.
 */
function npx_4grow_preprocess_html(array &$variables) {
  /* Add class to html tag */
  //$variables['html_attributes']->addClass('no-js');
  // Don't display the site name twice on the front page (and potentially others)
  /* if (isset($variables['head_title_array']['title']) && isset($variables['head_title_array']['name']) && ($variables['head_title_array']['title'] == $variables['head_title_array']['name'])) {
    $variables['head_title'] = $variables['head_title_array']['name'];
    } */

  /* Adding classes to body to know what type of training that is */
  $node = \Drupal::routeMatch()->getParameter('node');
  if ($node instanceof \Drupal\node\NodeInterface && $node->getType() == 'npx_training') {
    /* checking if there is image */
    //   kint($node->get('field_section_11')->referencedEntities());
    /*  checking the training type
     *  \Drupal\taxonomy\TermInterface $term */

    if (!is_null($node->get('field_company')) or count(reset($node->get('field_company')->referencedEntities()
        ->get('field_image')->referencedEntities())==0)) {
      $variables['attributes']['class'][] = 'company-no-logo';
    }
    $referencedEntities = $node->get('field_npx_training_type')->referencedEntities();
    $term = reset($referencedEntities);
    if ($term instanceof \Drupal\taxonomy\TermInterface) {
      $variables['attributes']['class'][] = 'node-npx-training-type-id-' . $term->id();
    }
  }
}

/**
 * Implements hook_page_attachments_alter().
 */
function npx_4grow_page_attachments_alter(array &$attachments) {

  $current_path = \Drupal::service('path.current')->getPath();
/*  $unsetLibraryPages = [
    "/node/208",
    '/comparison'
  ];*/
 // if (in_array($current_path, $unsetLibraryPages)){
    $library = $attachments['#attached']['library'];

    $library = array_flip($library);
    unset($library['aristotle/global']);
    $attachments['#attached']['library'] = array_keys($library);
 // }

}

/**
 * Implements hook_preprocess_HOOK() for Block document templates.
 */
function npx_4grow_preprocess_block(array &$variables) {
  if ($variables['base_plugin_id'] == 'system_branding_block') {
    $fileUrlGenerator = \Drupal::service('file_url_generator');
    $variables['site_logo'] = $fileUrlGenerator->transformRelative($fileUrlGenerator->generateString(theme_get_setting('logo.url')));
  }
  $node = \Drupal::routeMatch()->getParameter('node');
  /* fixing platon subtheme expanding sidebar bug */
  if (isset($variables['attributes']['id']) && $variables['attributes']['id'] == 'block-npx-4grow-lp-steps-block') {
    $variables['attributes']['id'] = 'block-lp-steps-block';
  }

  if ($node && $node->getType() == 'npx_training') {
    if (isset($variables['elements']['content']['#items'])
        && $variables['elements']['#plugin_id'] != 'innermenu_block'
        && $variables['elements']['#plugin_id'] != "field_block:node:npx_training:field_uwagi"
        && $variables['elements']['#plugin_id'] != "field_block:node:npx_training:field_average_ranking"
        && $variables['elements']['#plugin_id'] != "field_block:node:npx_training:field_training_program") {
      $term = $variables['elements']['content']['#items']->referencedEntities();
      foreach ($term as $t) {
        if ($t instanceof Paragraph) {
          switch ($t->getType()) {
            case 'icon_text':
            case 'text_indexed':
              if(empty($t->field_text_indexed->value)) {
                $variables['block_classes'] = 'hide';
              }
              break;
            case 'benefit':
              if(empty($t->field_benefit->referencedEntities())) {
                $variables['block_classes'] = 'hide';
              }
              break;
            case 'booking_rules':
              if(empty($t->field_text->value)) {
                $variables['block_classes'] = 'hide';
              }
              break;
          }
        }
      }
    }
  }
}

/**
 * Implements hook_preprocess_node().
 */
function npx_4grow_preprocess_node(array &$variables) {
  // Default to turning off byline/submitted.
  //$variables['display_submitted'] = FALSE;
  $view = views_embed_view('trenerzy', 'block_1');
  $variables['trainers_view'] = $view;

  /** @var \Drupal\node\NodeInterface $node */
  $node = $variables['node'];

  $view_builder = \Drupal::entityTypeManager()->getViewBuilder('node');
  switch ($node->bundle()){
    case 'trener':
      $variables['body_teaser'] = $node->body->view('teaser');
      break;
    case 'company_profile':
      $variables['reviews_count'] = \Drupal::service('npx.reviews.google')->getReviewsCount($node);
      break;
  }

  if ($variables['node']->bundle() === 'trener') {
    $variables['#attached']['library'][] = 'npx_4grow/trainer-page';
  }

  if ($variables['node']->bundle() === 'job_offer') {
    $variables['#attached']['library'][] = 'npx_4grow/job-offer';
  }

  if ($variables['node']->bundle() === 'coaching') {
    $variables['#attached']['library'][] = 'npx_4grow/coaching';
  }
  dump($variables['node']->bundle());

  $variables['szkolenia_firmy_block'] = [
    '#type' => 'view',
    '#name' => 'szkolenia_firmy',
    '#display_id' => 'block_1',
  ];
}

/**
 * Implements hook_theme_suggestions_views_view_alter().
 */
function npx_4grow_theme_suggestions_views_view_alter(array &$suggestions, array $variables) {
 $suggestions[] = sprintf('views_view__%s', $variables['view']->id());
}

/**
 * Implements hook_preprocess_views_view_unformatted().
 */
function npx_4grow_preprocess_views_view_unformatted(&$variables) {
  if ($variables["view"]->id() == "commerce_cart_form") {
    $view_mode = 'cart';
    $view_builder = \Drupal::entityTypeManager()->getViewBuilder('node');
    for($i=0;$i<count($variables['rows']);$i++) {
      $node = $variables['rows'][$i]['content']['#row']->_relationship_entities['npx_training'];
      if ($node) {

        $pre_render = $view_builder->view($node, $view_mode);
        $variables['rows'][$i]['training_cart'] = $pre_render;

        $product = $variables['rows'][$i]['content']['#row']->_relationship_entities['product_id'];
        $start_date = NpxDateUtils::cutDateString($product->get(TrainingDateFields::EVENT_DATE)[0]->value);

        $end_date = NpxDateUtils::cutDateString($product->get(TrainingDateFields::EVENT_DATE)[count($product->get(TrainingDateFields::EVENT_DATE)) - 1]->end_value);
        $date_display = NpxDateUtils::prepareDateDisplay($start_date, $end_date, false, true);
        $variables['rows'][$i]['date_display'] = $date_display;

        /** @var \Drupal\npx_discount\NpxCalculatorInterface $npxCalculator */
        $npxCalculator = \Drupal::service('npx_discount.calculator');
        $price_info = $npxCalculator->getPriceInfo($product, $variables['rows'][$i]['content']['#row']->_entity->getItems()[$i]->quantity->value);
        $variables['rows'][$i]['price_info'] = $price_info;
      }
    }
  } else if ($variables["view"]->id() == "confirm_message_product_display") {
    $product = $variables['rows'][0]['content']['#row']->_relationship_entities['product_id'];
    $quantity = $variables['rows'][0]['content']['#row']->_entity->get("quantity")->value;

    $start_date = NpxDateUtils::cutDateString($product->get(TrainingDateFields::EVENT_DATE)[0]->value);
    $end_date = NpxDateUtils::cutDateString($product->get(TrainingDateFields::EVENT_DATE)[count($product->get(TrainingDateFields::EVENT_DATE))-1]->end_value);
    $date_display = NpxDateUtils::prepareDateDisplay($start_date, $end_date, false, true);
    $variables['rows'][0]['date_display'] = $date_display;

    /** @var \Drupal\npx_discount\NpxCalculatorInterface $npxCalculator */
    $npxCalculator = \Drupal::service('npx_discount.calculator');
    $price_info = $npxCalculator->getPriceInfo($product,$quantity);
    $variables['rows'][0]['price_info'] = $price_info;
  }
}

/**
 * Implements hook_theme_suggestions_views_view_unformatted_alter().
 */
function npx_4grow_theme_suggestions_views_view_unformatted_alter(array &$suggestions, array $variables) {
  $suggestions[] = sprintf('views_view_unformatted__%s', $variables['view']->id());
}

/**
 * Implements theme_preprocess_commerce_order_total_summary(().
 */
function npx_4grow_preprocess_commerce_order_total_summary(&$variables) {
  $order = $variables['order_entity'];
  if (!empty($order)){
    $price = [
      "discounted" => 0,
      "discounted_netto" => 0,
      "discount" => 0,
      "discount_netto" => 0,
      "regular" => 0,
      "regular_netto" => 0,
    ];

    /** @var \Drupal\npx_discount\NpxCalculatorInterface $npxCalculator */
    $npxCalculator = \Drupal::service('npx_discount.calculator');
    foreach ($order->getItems() as $item) {
      $quantity = $item->get("quantity")->value;
      $product = $item->get("purchased_entity")[0]?->entity?->get("product_id")[0]?->entity;
      if ($product) {
        $price_info = $npxCalculator->getPriceInfo($product, $quantity);
        $price['discounted'] += $price_info['discounted'];
        $price['discounted_netto'] += $price_info['discounted_netto'];
        $price['discount'] += $price_info['discount'];
        $price['discount_netto'] += $price_info['discount_netto'];
        $price['regular'] += $price_info['regular'];
        $price['regular_netto'] += $price_info['regular_netto'];
      }
    }
    $variables['price_info'] = $price;
  }
}

/**
 * Implements hook_preprocess_form().
 */
function npx_4grow_preprocess_form(array &$variables) {
  //$variables['attributes']['novalidate'] = 'novalidate';
}

/**
 * Implements hook_preprocess_select().
 */
function npx_4grow_preprocess_select(array &$variables) {
  //$variables['attributes']['class'][] = 'select-chosen';
}

/**
 * Implements hook_preprocess_field().
 */
function npx_4grow_preprocess_field(array &$variables, $hook) {
  if($variables['field_name'] == 'field_text_html_1') {
    $variables['items'][0]['entity_id'] = $variables['element']['#object']->id();
  }

  if ($variables['element']['#bundle'] == 'npx_training') {
    if ($variables['field_name'] == 'field_section_05') {
      if (empty($variables['element']['#items']->referencedEntities()[0]->field_text_indexed->value)) {
        $variables['attributes']['class'][] = 'hide';
      }
    }
  }
  if ($variables['field_name'] == 'field_image' && $variables['label'] == 'Obraz') {
    $is_default = $variables['items'][0]['content']['#item']->_is_default;
    if ($is_default == true) {
      $node_id = $variables['items'][0]['content']['#item']->getParent()->getParent()->getEntity()->get('parent_id')->getValue()[0]['value'];
      $variables['default_img'] = true;
      $node = Node::load($node_id);
      if ($node && $node->bundle()=='npx_training') {
        $variables['company_name'] = $node->get('field_company')->referencedEntities()[0]->get('title')->getValue()[0]['value'];
      }
    }
  }

  if ($variables['field_name'] == 'field_benefit') {
    $benefits = $variables['element']['#items']->referencedEntities();
    // kint($benefits);
    $i = 1;
    foreach ($benefits as $benefit) {
      if ($benefit->id() == '15') {
        //  kint($benefit->id());
        $variables['attributes']['class'][] = 'hide-' . $i;
      }
      $i++;
    }
  }
}

/**
 * Implements hook_preprocess_HOOK().
 */
function npx_4grow_preprocess_field__field_text_html_1(&$variables) {
  /** @var \Drupal\paragraphs\Entity\Paragraph $paragraph */
  $paragraph = $variables['element']['#object'];
  if($paragraph->bundle() === 'benefit') {
    $variables['entity_id'] = $paragraph->id();
  }
}

/**
 * Implements hook_preprocess_details().
 */
function npx_4grow_preprocess_details(array &$variables) {
  /* $variables['attributes']['class'][] = 'details';
    $variables['summary_attributes']['class'] = 'summary'; */
}

/**
 * Implements hook_theme_suggestions_details_alter().
 */
function npx_4grow_theme_suggestions_details_alter(array &$suggestions, array $variables) {

}

/**
 * Implements hook_preprocess_menu_local_task().
 */
function npx_4grow_preprocess_menu_local_task(array &$variables) {
  //$variables['element']['#link']['url']->setOption('attributes', ['class'=>'rounded']);
}

/**
 * Implements hook_theme_suggestions_HOOK_alter for blocks.
 */
function npx_4grow_theme_suggestions_block_alter(&$suggestions, $variables) {
  // Load theme suggestions for blocks from parent theme.
  foreach ($suggestions as &$suggestion) {
    $suggestion = str_replace('npx_4grow_', 'aristotle_', $suggestion);
  }
}

/**
 * Implements hook_theme_preprocess_paragraph.
 */
function npx_4grow_preprocess_paragraph(&$variables){
  /** @var \Drupal\paragraphs\ParagraphInterface $paragraph */
  $paragraph = $variables['paragraph'];
  // Get the type
  if ($paragraph->bundle() == "title_subtile" && !is_null($paragraph->getParentEntity())) {
    $node_id = $variables['paragraph']->getParentEntity()->id();
    $variables['node_id'] = $node_id;
  }
  else if ($paragraph->bundle() === 'trainer_with_video') {
    $current_path = \Drupal::service('path.current')->getPath();

    // Check if the path matches the pattern.
    if (preg_match('#^/npx_offer/get_offer_list/\d+$#', $current_path)) {
      $variables['attributes']['class'][] = 'views-field';
      $variables['attributes']['class'][] = 'views-field-trainer';
      $variables['attributes']['class'][] = 't2b';
    }
  }
}

/**
 * Implements hook_theme_preprocess_breadcrumb.
 */
function npx_4grow_preprocess_breadcrumb(&$variables) {
  $route_match = \Drupal::routeMatch();
  $node = $route_match->getParameter('node');
  if (!is_null($node)) {
    $variables['breadcrumb'][1] = null;
    if ($node->bundle()=='npx_training') {
      $page_title = $node->get('field_section_01')->referencedEntities()[0]->field_text_1[0]->value;
      $variables['node_type_text'] = t('Szkolenia');
      $variables['node_type_url'] = '/npx-search/form?text_phrase=&training_type=&training_city=';
    }
    else {
      if ($node->bundle()=='company_profile') {
        $variables['node_type_text'] = t('Firmy szkoleniowe');
      }
      $request = \Drupal::request();
      $page_title = \Drupal::service('title_resolver')->getTitle($request, $route_match->getRouteObject());
    }
    $variables['breadcrumb'][1] = \Drupal\Core\Link::createFromRoute($page_title, '<none>');
    $variables['#cache']['contexts'][] = 'url';
  }
  else {
    $request = \Drupal::request();
    $page_title = \Drupal::service('title_resolver')->getTitle($request, $route_match->getRouteObject());
    if($page_title instanceof TranslatableMarkup) {
      if ($page_title->getUntranslatedString() == 'Checkout') {
        unset($variables['breadcrumb'][2]);
      }
    } else {
      if ($page_title == 'Checkout') {
        unset($variables['breadcrumb'][2]);
      }
    }
    $variables['breadcrumb'][1] = \Drupal\Core\Link::createFromRoute($page_title, '<none>');
    $variables['#cache']['contexts'][] = 'url';
  }
}

/**
 * Preprocess page title
 */
function npx_4grow_preprocess_page_title(&$variables) {
  if ($node = \Drupal::routeMatch()->getParameter('node')) {
    $hiddenBundles = [
      'landing_page_dla_firmy_szkolenio',
      'pricing'
    ];
    // Load the label of the bundle
    if (in_array($node->bundle(), $hiddenBundles)) {
      unset($variables['title']);
    }
  }
}

function npx_4grow_preprocess_views_view_field(&$variables) {
  $view = $variables['view'];
  if($view->id() == 'commerce_cart_form') {
    if($variables['field']->field == 'nid') {
      $nid = $variables['field']->getValue($variables['row']);
      $product_entity = Node::load($nid);
      $favorite_block = _npx_favorites_block($product_entity);
      $variables['output'] = \Drupal::service('renderer')->render($favorite_block);
    }
  }
}

function npx_4grow_preprocess_page__make_reservation(&$variables)
{
  $route_match = \Drupal::routeMatch();

  $product = $route_match->getParameter('product');
  $variables['npx_training_url'] = '#';
  if ($product instanceof Product) {
    $variables['is_commerce_product'] = TRUE;
    // Get the value of the 'npx_training' field.
    $training_id = $product->get('npx_training')->getValue()[0]['target_id'];
    // Check if the training ID is valid.
    if (is_numeric($training_id)) {
      // Load the node using the training ID.
      $training_node = Node::load($training_id);
      // Check if the node exists and its bundle is 'npxtraining'.
      if ($training_node && $training_node->bundle() == 'npx_training') {
        $url = Url::fromRoute('entity.node.canonical', ['node' => $training_id], ['absolute' => FALSE]);
        $variables['npx_training_url'] = $url->toString(TRUE)->getGeneratedUrl();
      }
    }
  }
}

/**
 * Implements hook_views_pre_render().
 */
function npx_4grow_views_pre_render(ViewExecutable $view) {
  if (isset($view) && ($view->storage->id() == 'coaches')) {
    $view->element['#attached']['library'][] = 'npx_4grow/coaches-view';
  }
  if (isset($view) && ($view->storage->id() == 'job_listing')) {
    $view->element['#attached']['library'][] = 'npx_4grow/job-list-view';
  }
}

/**
 * Implements hook_preprocess_HOOK() for page templates.
 */
function npx_4grow_preprocess_page(array &$variables) {
  if (empty($variables['page']['footer_top'])) {
    $variables['page']['footer_top'] = [
      '#type' => 'container',
      '#attributes' => ['class' => ['region', 'region-footer_top', 'placeholder']],
    ];
  }
}

function npx_4grow_theme_suggestions_datetime_wrapper_alter(array &$suggestions, array $variables) {
  if (isset($variables['element'])) {
    $element = $variables['element'];

    if (!empty($element['#name'])) {
      $name_sanitized = preg_replace('/[^a-zA-Z0-9_]+/', '_', $element['#name']);
      $name_sanitized = trim($name_sanitized, '_');
      $suggestions[] = 'datetime_wrapper__' . $name_sanitized;
    }

    if (!empty($element['#id'])) {
      $id_sanitized = str_replace('-', '_', $element['#id']);
      $suggestions[] = 'datetime_wrapper__' . $id_sanitized;
    }
  }
}

function npx_4grow_preprocess_form_element(&$variables) {
  $element = $variables['element'];
  $variables['tooltip'] = FALSE;

  if (isset($element['#activate_bootstrap_tooltip'])) {
    $variables['tooltip'] = TRUE;
  }

}

function npx_4grow_preprocess_datetime_wrapper(&$variables){
  $element = $variables['element'];
  $variables['tooltip'] = FALSE;

  if (isset($element['#activate_bootstrap_tooltip'])) {
    $variables['tooltip'] = TRUE;
  }
}

/**
 * Implements hook_theme_suggestions_HOOK_alter() for page templates.
 */
function npx_4grow_theme_suggestions_page_alter(array &$suggestions, array $variables) {
  $route_name = \Drupal::routeMatch()->getRouteName();
  if ($route_name == 'node.add') {
    $node_type = \Drupal::routeMatch()->getParameter('node_type');
    if ($node_type instanceof \Drupal\node\NodeTypeInterface) {
      $node_type_bundle = $node_type->id();
      if ($node_type_bundle == 'npx_inquiry') {
        $suggestions[] = 'page__npx_inquiry_form_layout';
      }
    }
  }
  elseif ($route_name == 'entity.node.edit_form') {
    $node = \Drupal::routeMatch()->getParameter('node');
    if ($node instanceof \Drupal\node\NodeInterface) {
      if ($node->bundle() == 'npx_inquiry') {
        $suggestions[] = 'page__npx_inquiry_form_layout';
      }
    }
  }
}

