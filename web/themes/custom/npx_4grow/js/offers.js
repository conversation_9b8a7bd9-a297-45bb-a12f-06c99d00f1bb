(function ($, Drupal, drupalSettings, window, document) {
  ("use strict");

  /********************************************************************
   * CONSTANTS & SELECTORS
   ********************************************************************/
  const SEL = {
    sectionBlock: ".section-block",
    sectionContent: ".section-block__content",
    sectionHeader: ".section-block__header",
    podstawoweSlug: "informacje-podstawowe",
    podstawoweSelector: ".section-block--informacje-podstawowe",
    offersList: ".view-inquiry-offers-list",
    viewContentWrapper: ".view-inquiry-offers-list .view-content-wrapper",
    viewContent: ".view-inquiry-offers-list .view-content",
    viewItems: ".view-inquiry-offers-list .view-content .view-items",
    viewLabels: ".view-inquiry-offers-list .view-labels",
    counterValue: ".npx-counter-value",
    rejectedItems: ".view-items.view-items-offer-rejected",
    shareFooter: ".npx-share-footer",
    colorboxInline: ".colorbox-inline",
    trainerArticle: ".views-field-trainer article",
  };

  const CLASS = {
    collapsedContainer: "collapsed-container",
    collapsedHeader: "collapsed",
    dNone: "d-none",
    mainOffer: "npx-main-offer",
    subOffer: "npx-sub-offer",
    withdrawn: "offer-withdrawn",
    dragOver: "drag-over",
    stickyActiveBody: "npx-sticky-sections-active",
    invisible: "invisible",
    subofferExpander: "npx-suboffer-expander",
    subofferExpand: "npx-suboffer-expand",
    subofferCollapse: "npx-suboffer-collapse",
    positionPrefix: "position",
  };

  const ATTR = {
    dataSection: "data-section",
    dataPid: "data-pid",
    originalTop: "original-top",
    originalWidth: "original-width",
    isSticky: "isSticky",
  };

  /********************************************************************
   * UTILITIES
   ********************************************************************/
  const Utils = {
    debounce(fn, wait) {
      let t;
      return function debounced(...args) {
        clearTimeout(t);
        t = setTimeout(() => fn.apply(this, args), wait);
      };
    },
    raf(fn) {
      return window.requestAnimationFrame(fn);
    },
    schedule(fn, delay = 0) {
      return setTimeout(fn, delay);
    },
    $$(selector, ctx = document) {
      return Array.from(ctx.querySelectorAll(selector));
    },
    $1(selector, ctx = document) {
      return ctx.querySelector(selector);
    },
    parseIntSafe(v, def = 0) {
      const n = parseInt(v, 10);
      return Number.isNaN(n) ? def : n;
    },
    nextAnimationFrame() {
      return new Promise((res) => requestAnimationFrame(res));
    },
  };

  /********************************************************************
   * SCHEDULERS (batch equal height recalculations)
   ********************************************************************/
  const HeightScheduler = (function () {
    const queueDelays = [0, 100, 250, 500]; // original scattered timeouts consolidated
    function scheduleEqualHeights() {
      queueDelays.forEach((d) =>
        Utils.schedule(() => {
          Drupal.behaviors.npx4growOffersEqualHeight.equalHeightRefresh();
        }, d)
      );
    }
    return { scheduleEqualHeights };
  })();

  // Debounced one-shot equal heights to prevent jumpiness during batch/sticky updates.
  const EqualHeightsScheduler = (function () {
    let t = null;
    function request(delay = 80) {
      clearTimeout(t);
      t = setTimeout(() => {
        Drupal.behaviors.npx4growOffersEqualHeight.equalHeightRefresh();
      }, delay);
    }
    return { request };
  })();

  /********************************************************************
   * COLORBOX
   ********************************************************************/
  function initializeColorbox(context) {
    $(
      once("npx-4grow-offers-colorbox-init", SEL.colorboxInline, context)
    ).colorbox({
      inline: true,
      width: "50%",
      height: "50%",
    });
  }

  /********************************************************************
   * TRAINER BODY TRIM + SHOW MORE
   ********************************************************************/
  const TrainerBody = {
    trim(context) {
      $(".field-body-full.short-desc", context).each(function () {
        const $el = $(this);
        const full = $el.text().trim();
        if (full.length > 50) {
          $el.text(full.substring(0, 50) + "...");
        }
      });
    },
    showMore(context) {
      $(".show-more", context).on("click", function (e) {
        e.preventDefault();
        const html = $(this).siblings(".full-body-content").html();
        $.colorbox({
          html,
          width: "80%",
          height: "80%",
        });
      });
    },
  };

  /********************************************************************
   * COUNTER INPUT REPLACEMENT
   ********************************************************************/
  function replaceSpanWithInput(context) {
    once(
      "npx-4grow-offers-replace-span-with-input",
      SEL.counterValue,
      context
    ).forEach((node) => {
      const $span = $(node);
      const pid = $span.data("pid");
      const val = $span.text();
      const $input = $("<input>", {
        type: "text",
        inputmode: "numeric",
        pattern: "[0-9]*",
        // replicate class for selectors relying on it
        class: "npx-counter-value",
        "data-pid": pid,
        value: val,
      });
      $span.replaceWith($input);
    });
  }

  /********************************************************************
   * OFFER LIST CLASS GROUPING (main / sub / withdrawn sets)
   ********************************************************************/
  function groupOfferSets(context) {
    let offerSetId = 1;
    $(`${SEL.offersList} .view-content .view-items`, context).each(function () {
      const $item = $(this);
      const hasCounter = $item.find(SEL.counterValue).length > 0;
      const isWithdrawn = $item.find(".offer-withdrawn-message").length > 0;
      let next = $item.next(".view-items");
      const subItems = [];
      let foundGap = false;

      if (hasCounter) {
        while (next.length && next.find(SEL.counterValue).length === 0) {
          subItems.push(next);
          next = next.next(".view-items");
          foundGap = true;
        }
        if (foundGap) {
          $item.addClass(`${CLASS.mainOffer} npx-offer-set-${offerSetId}`);
          subItems.forEach(($s) => {
            $s.addClass(`npx-offer-set-${offerSetId}`);
            if (
              isWithdrawn &&
              $s.find(".views-field-offer-comment-edit .form-submit").length ===
                0
            ) {
              $s.addClass(CLASS.withdrawn);
            }
          });
          offerSetId++;
        }
      } else {
        $item.addClass(CLASS.subOffer);
      }
      if (isWithdrawn) $item.addClass(CLASS.withdrawn);
    });
  }

  /********************************************************************
   * SECTION COLLAPSING (excluding podstawowe)
   ********************************************************************/
  const SectionsToggle = {
    init(context) {
      // open all contents initially
      Utils.$$(SEL.sectionContent, context).forEach((c) => {
        c.style.maxHeight = c.scrollHeight + "px";
      });

      once(
        "npx4growOfferSection",
        `.view-labels ${SEL.sectionHeader}:not([data-section="${SEL.podstawoweSlug}"])`,
        context
      ).forEach((hdr) => {
        hdr.addEventListener("click", () => {
          const slug = hdr.dataset.section;
          if (!slug) return;

          document
            .querySelectorAll(`.section-block--${slug}`)
            .forEach((container) => {
              const isCollapsed = container.classList.toggle(
                CLASS.collapsedContainer
              );
              const content = container.querySelector(SEL.sectionContent);
              if (!content) return;

              if (isCollapsed) {
                content.style.maxHeight = null;
                Utils.schedule(() => $(content).addClass(CLASS.dNone), 410);
              } else {
                $(content).removeClass(CLASS.dNone);
                content.offsetHeight; // force reflow
                content.style.maxHeight = content.scrollHeight + "px";
              }
            });

          hdr.classList.toggle(CLASS.collapsed);

          HeightScheduler.scheduleEqualHeights();
        });
      });
    },
  };

  /********************************************************************
   * EQUAL HEIGHTS (original logic preserved, reorganized)
   ********************************************************************/
  const EqualHeights = {
    equalizeCompanyFieldHeights() {
      // original function preserved
      $(".views-field-npx_offer_profile_company_views_field").css("height", "");
      let max = 0;
      $(
        ".views-field-npx_offer_profile_company_views_field .company-name-for-filters"
      ).each(function () {
        max = Math.max(max, $(this).innerHeight());
      });
      if (max > 0) {
        $(".views-field-npx_offer_profile_company_views_field").css(
          "height",
          max + 10 + "px"
        );
      }
      if (
        $(
          ".views-field-npx_offer_actions_views_field .npx-offer-restore-container"
        ).length > 0
      ) {
        $(".views-field-npx_offer_actions_views_field").css("height", "84px");
      } else {
        $(".views-field-npx_offer_actions_views_field").css("height", "60px");
      }

      // podstawowe section heights
      let totalHeight = 0;
      Utils.schedule(() => {
        $(
          `.view-labels .section-block--${SEL.podstawoweSlug}.section-block`
        ).each(function () {
          $(this).css("height", "");
          $(this)
            .find(".view-label:visible")
            .each(function () {
              totalHeight += $(this).outerHeight(true);
            });
        });
        if (totalHeight > 0) {
          totalHeight += 20;
          $(`.section-block.section-block--${SEL.podstawoweSlug}`).css(
            "height",
            totalHeight + "px"
          );
          $(
            `.section-block.section-block--${SEL.podstawoweSlug} ${SEL.sectionContent}`
          ).css("height", totalHeight + "px");
        }
      }, 100);
    },

    equalHeightRefresh() {
      // Original algorithm preserved (slightly compacted for readability)
      const groups = {};
      document
        .querySelectorAll(`.section-block:not([style*="display: none"])`)
        .forEach((wrapper) => {
          if (wrapper.offsetParent === null) return;
          const slugClass = Array.from(wrapper.classList).find((c) =>
            c.startsWith("section-block--")
          );
          if (!slugClass) return;
          (groups[slugClass] = groups[slugClass] || []).push(wrapper);
        });

      // headers
      Object.values(groups).forEach((wrappers) => {
        const headers = wrappers
          .map((w) => w.querySelector(SEL.sectionHeader))
          .filter((h) => h && h.offsetParent !== null);
        headers.forEach((h) => (h.style.height = "auto"));
        const maxH = Math.max(
          ...headers.map((h) => h.getBoundingClientRect().height)
        );
        headers.forEach((h) => (h.style.height = maxH + "px"));
      });

      // rows
      Object.values(groups).forEach((wrappers) => {
        const contents = wrappers
          .map((w) => w.querySelector(SEL.sectionContent))
          .filter((c) => c && c.offsetParent !== null);
        if (!contents.length) return;

        const maxRows = contents.reduce(
          (m, c) => Math.max(m, c.children.length),
          0
        );

        for (let i = 0; i < maxRows; i++) {
          contents.forEach((c) => {
            const cell = c.children[i];
            if (cell) cell.style.height = "auto";
          });

          const maxRowH = contents.reduce((m, c) => {
            const cell = c.children[i];
            if (!cell || cell.offsetParent === null) return m;
            if (
              cell.classList.contains(
                "views-field-npx_offer_actions_views_field"
              )
            ) {
              if ($(cell).find(".npx-offer-rejected").length > 0) {
                return Math.max(m, 71);
              }
              const buttonContainer = cell.querySelector(".dropbutton-widget");
              let baseHeight = buttonContainer ? 40 : 0;
              const contactContainer = cell.querySelector(
                ".views-field-contact-data"
              );
              if (
                contactContainer &&
                contactContainer.style.display !== "none"
              ) {
                const clone = contactContainer.cloneNode(true);
                Object.assign(clone.style, {
                  display: "block",
                  position: "absolute",
                  visibility: "hidden",
                });
                document.body.appendChild(clone);
                const h = clone.getBoundingClientRect().height;
                document.body.removeChild(clone);
                baseHeight += h + 10;
              }
              return Math.max(m, baseHeight);
            }
            return Math.max(m, cell.getBoundingClientRect().height);
          }, 0);

          contents.forEach((c) => {
            const cell = c.children[i];
            if (cell && cell.offsetParent !== null) {
              cell.style.height = maxRowH + "px";
              if (
                cell.classList.contains(
                  "views-field-npx_offer_actions_views_field"
                )
              ) {
                cell.style.overflow = "visible";
              }
            }
          });
        }
      });

      // block content totals + wrappers + trainer articles
      Object.values(groups).forEach((wrappers) => {
        const contents = wrappers
          .map((w) => w.querySelector(SEL.sectionContent))
          .filter(
            (c) =>
              c && c.offsetParent !== null && !c.classList.contains(CLASS.dNone)
          );
        if (!contents.length) return;

        contents.forEach((c) => {
          c.style.height = "";
          c.style.maxHeight = "";
        });

        contents.forEach((c) => {
          c.offsetHeight;
          const sum = Array.from(c.children).reduce((acc, row) => {
            if (
              row.offsetParent !== null &&
              getComputedStyle(row).display !== "none"
            ) {
              return acc + (parseFloat(row.style.height) || row.offsetHeight);
            }
            return acc;
          }, 0);
          const cs = getComputedStyle(c);
          const total =
            sum +
            (parseFloat(cs.paddingTop) || 0) +
            (parseFloat(cs.paddingBottom) || 0);
          c.style.height = total + "px";
          c.style.maxHeight = total + "px";
        });

        wrappers.forEach((w) => (w.style.height = ""));
        const maxWrapperH = wrappers.reduce((m, w) => {
          if (
            w.offsetParent !== null &&
            getComputedStyle(w).display !== "none" &&
            !w.classList.contains(CLASS.collapsedContainer)
          ) {
            return Math.max(m, w.getBoundingClientRect().height);
          }
          return m;
        }, 0);
        wrappers.forEach((w) => {
          if (
            w.offsetParent !== null &&
            getComputedStyle(w).display !== "none" &&
            !w.classList.contains(CLASS.collapsedContainer)
          ) {
            w.style.height = maxWrapperH + "px";
          }
        });

        // trainer article row equalization
        (function equalizeTrainerArticles() {
          const rowGroups = {};
          const rowTolerance = 5;
          const articles = document.querySelectorAll(SEL.trainerArticle);
          articles.forEach((a) => {
            a.style.height = "auto";
            a.style.minHeight = "auto";
          });
          if (articles.length) articles[0].offsetHeight;
          articles.forEach((a) => {
            const top = a.getBoundingClientRect().top;
            const rowY = Math.round(top / rowTolerance) * rowTolerance;
            (rowGroups[rowY] = rowGroups[rowY] || []).push(a);
          });
          Object.values(rowGroups).forEach((row) => {
            if (row.length < 2) return;
            const mh = Math.max(
              ...row.map((a) => a.getBoundingClientRect().height)
            );
            row.forEach((a) =>
              a.setAttribute("style", `height:${mh}px !important`)
            );
          });
        })();
      });
    },
  };

  /********************************************************************
   * COUNTERS + DRAG (logic mostly preserved, optimized scheduling)
   ********************************************************************/
  const Counters = {
    ajaxUpdateCounters() {
      // preserve original sequence
      const currentPositions = {};
      $(".view-items .section-block--informacje-podstawowe").each(function () {
        const $el = $(this);
        if ($el.css("position") === "fixed") {
          const match = $el.attr("class").match(/position(\d+)/);
          if (match) {
            currentPositions[parseInt(match[1], 10)] = {
              left: $el.css("left"),
              element: $el,
            };
          }
        }
      });

      // Optional: keep as-is or remove; not required for realign
      Object.values(currentPositions).forEach((info) => {
        try {
          info.element.data("lockedLeft", info.left);
        } catch (e) {}
      });

      const $counters = $(SEL.counterValue);
      $counters.each(function (i) {
        $(this).val(i + 1);
      });

      const payload = $counters
        .map(function () {
          return {
            pid: $(this).data("pid"),
            value: $(this).val(),
          };
        })
        .get();

      $.ajax({
        url: drupalSettings.path.baseUrl + "offer/update_counters",
        type: "POST",
        contentType: "application/json",
        data: JSON.stringify({ counters: payload }),
      });

      if (Drupal.behaviors.npx4growOffersPositionClasses) {
        Drupal.behaviors.npx4growOffersPositionClasses.assignPositionClasses();
      }

      // Update batch classes after any reordering.
      updateBatchClassesForViewport();

      // Re-assign positions again after batch recalculation (active set may change)
      if (Drupal.behaviors.npx4growOffersPositionClasses) {
        Drupal.behaviors.npx4growOffersPositionClasses.assignPositionClasses();
      }

      // Clear cached metrics on non-sticky items and drop any stale lockedLeft
      $(
        `.view-items ${SEL.podstawoweSelector}, .view-labels ${SEL.podstawoweSelector}`
      ).each(function () {
        const $el = $(this);
        if (!$el.data(ATTR.isSticky)) {
          $el.removeData(ATTR.originalTop).removeData(ATTR.originalWidth);
        }
        $el.removeData("lockedLeft");
      });

      // Refresh sticky metrics after DOM reordering and batch recalculation
      if (
        Drupal.behaviors.npx4growOffersStickySections &&
        Drupal.behaviors.npx4growOffersStickySections
          .refreshStickySectionPositions
      ) {
        Utils.raf(() =>
          Drupal.behaviors.npx4growOffersStickySections.refreshStickySectionPositions()
        );
        Utils.schedule(
          () =>
            Drupal.behaviors.npx4growOffersStickySections.refreshStickySectionPositions(),
          120
        );
      }

      // Realign already-sticky ones
      Utils.raf(StickySections.realignStickyLefts);
      Utils.schedule(StickySections.realignStickyLefts, 150);
      Utils.schedule(StickySections.realignStickyLefts, 350);

      // Remove previous “restore old left” block; new left is computed from new order
      // NEW: do not schedule stickySections immediately here; let scroll/resize drive it

      afterAction();
    },

    moveToLastPosition($item) {
      if (!$item || !$item.length) return;
      const setClass = Counters._getOfferSetClass($item);
      const $itemsToMove = setClass ? $("." + setClass) : $item;
      const $last = $(SEL.viewItems).last();
      if ($last.is($item) || (setClass && $last.hasClass(setClass))) return;

      $item.find(SEL.counterValue).val($(SEL.viewItems).length);
      $itemsToMove.detach().insertAfter($last);
      Utils.raf(() => {
        Counters.ajaxUpdateCounters();
        HeightScheduler.scheduleEqualHeights();
      });
    },

    moveToFirstPosition($item) {
      if (!$item || !$item.length) return;
      const setClass = Counters._getOfferSetClass($item);
      const $itemsToMove = setClass ? $("." + setClass) : $item;
      $item.find(SEL.counterValue).val(1);
      const $first = $(SEL.viewItems).first();
      $itemsToMove.detach().insertBefore($first);
      Utils.raf(() => {
        Counters.ajaxUpdateCounters();
        HeightScheduler.scheduleEqualHeights();
      });
    },

    _getOfferSetClass($item) {
      return (
        $item
          .attr("class")
          ?.split(" ")
          .find((c) => c.startsWith("npx-offer-set-")) || null
      );
    },

    initMoveButtons(context) {
      once(
        "npx-4grow-offers-changing-counters-move",
        ".move-to-next, .move-to-prev",
        context
      ).forEach((btn) => {
        $(btn).on("click", function () {
          const $current = $(this).closest(".view-items");
          const setClass = Counters._getOfferSetClass($current);
          const isNext = $(this).hasClass("move-to-next");

          function findNext($base) {
            let n = $base.next(".view-items");
            while (
              n.length &&
              (!n.find(SEL.counterValue).length ||
                n.hasClass(CLASS.mainOffer)) &&
              !n.next(".view-items").find(SEL.counterValue).length
            ) {
              n = n.next(".view-items");
            }
            return n.length ? n : $base.siblings(".view-items").last();
          }
          function findPrev($base) {
            let p = $base.prev(".view-items");
            while (p.length && !p.find(SEL.counterValue).length) {
              p = p.prev(".view-items");
            }
            return p.length ? p : $base.siblings(".view-items").first();
          }

          if (setClass) {
            const $group = $("." + setClass);
            if (isNext) {
              const $next = findNext($group.last());
              $group.insertAfter($next.length ? $next : $current);
            } else {
              const $prev = findPrev($group.first());
              $group.insertBefore($prev.length ? $prev : $current);
            }
          } else {
            const target = isNext ? findNext($current) : findPrev($current);
            if (target.length) {
              isNext
                ? $current.insertAfter(target)
                : $current.insertBefore(target);
            }
          }

          Counters.ajaxUpdateCounters();
          HeightScheduler.scheduleEqualHeights();
        });
      });
    },

    initCounterInputs(context) {
      once(
        "npx-4grow-offers-changing-counters-value",
        SEL.counterValue,
        context
      ).forEach((inp) => {
        let prevVal;
        const $input = $(inp);

        function handleChange() {
          const val = Utils.parseIntSafe($input.val());
          const $item = $input.closest(".view-items");
          const setClass = Counters._getOfferSetClass($item);

          const $target = $(SEL.viewItems).filter(function () {
            return (
              !$(this).hasClass("npx-sub-offer-class") &&
              Utils.parseIntSafe($(this).find(SEL.counterValue).val()) ===
                val &&
              this !== $item[0]
            );
          });

          if (prevVal !== val) {
            if (setClass) {
              const $set = $("." + setClass).detach();
              Counters._reinsertByValue(
                $item,
                $set,
                $target,
                prevVal,
                val,
                setClass
              );
            } else {
              const $detached = $item.detach();
              Counters._reinsertByValue(
                $item,
                $detached,
                $target,
                prevVal,
                val,
                null
              );
            }
          }
          Counters.ajaxUpdateCounters();
          HeightScheduler.scheduleEqualHeights();
        }

        $input
          .on("focus", function () {
            prevVal = Utils.parseIntSafe($(this).val());
          })
          .on("keypress", function (e) {
            if (e.which === 13) handleChange();
          })
          .on("blur", handleChange);
      });
    },

    _reinsertByValue($item, $block, $target, prevVal, newVal, setClass) {
      if ($target.length && $target[0] !== $item[0]) {
        if (prevVal < newVal) {
          if ($target.hasClass(CLASS.mainOffer)) {
            const $nextWithCounter = $target
              .nextAll(".view-items")
              .filter(function () {
                return $(this).find(SEL.counterValue).length > 0;
              })
              .first();
            if ($nextWithCounter.length) {
              $block.insertBefore($nextWithCounter);
            } else {
              $block.insertAfter($(SEL.viewItems).last());
            }
          } else {
            $block.insertAfter($target);
          }
        } else {
          $block.insertBefore($target);
        }
      } else {
        if (newVal >= 1) {
          const $firstRejected = $(SEL.rejectedItems).first();
          if ($firstRejected.length) {
            $block.insertBefore($firstRejected);
          } else {
            $block.insertAfter($(SEL.viewItems).last());
          }
        } else {
          $block.insertBefore($(SEL.viewItems).first());
        }
      }
    },

    initDragAndDrop(context) {
      let dragged = null;
      let setClass = null;

      $(SEL.viewItems, context).each(function () {
        $(this).attr(
          "draggable",
          !$(this).hasClass("view-items-offer-rejected")
        );
      });

      $(document)
        .off("dragstart.offers dragend.offers")
        .on("dragstart.offers", SEL.viewItems, function () {
          dragged = this;
          setClass = Counters._getOfferSetClass($(dragged));
          Utils.schedule(() => {
            $(dragged).hide();
            if (setClass) $("." + setClass).hide();
          });
        })
        .on("dragend.offers", SEL.viewItems, function () {
          Utils.schedule(() => {
            $(dragged).show();
            if (setClass) $("." + setClass).show();
            dragged = null;
            setClass = null;
          });
        });

      $(SEL.viewContent)
        .off("dragover.offers dragenter.offers")
        .on("dragover.offers dragenter.offers", function (e) {
          e.preventDefault();
        });

      $(document)
        .off("drop.offers")
        .on("drop.offers", function (e) {
          e.preventDefault();
          if (!dragged) return;
          const dropX = e.originalEvent.clientX;
          let dropped = false;

          $(".view-labels").each(function () {
            const $this = $(this);
            const left = $this.offset().left;
            const right = left + $this.outerWidth();
            if (dropX > left && dropX < right) {
              if (setClass) {
                $("." + setClass)
                  .detach()
                  .insertBefore($(SEL.viewItems).first());
              } else {
                $(dragged).detach().insertBefore($(SEL.viewItems).first());
              }
              dropped = true;
              return false;
            }
          });

          if (!dropped) {
            $(SEL.viewItems).each(function () {
              const $this = $(this);
              const left = $this.offset().left;
              const right = left + $this.outerWidth();
              if (dropX > left && dropX < right && dragged !== this) {
                if ($this.hasClass("view-items-offer-rejected")) {
                  const $firstRejected = $(SEL.rejectedItems).first();
                  if ($firstRejected.length) {
                    if (setClass) {
                      $("." + setClass)
                        .detach()
                        .insertBefore($firstRejected);
                    } else {
                      $(dragged).detach().insertBefore($firstRejected);
                    }
                  }
                } else {
                  if (setClass) {
                    $("." + setClass)
                      .detach()
                      .insertBefore($this);
                  } else {
                    $(dragged).detach().insertBefore($this);
                  }
                }
                dropped = true;
                return false;
              }
            });
          }

          if (!dropped) {
            const $firstRejected = $(SEL.rejectedItems).first();
            if ($firstRejected.length) {
              if (setClass) {
                $("." + setClass)
                  .detach()
                  .insertBefore($firstRejected);
              } else {
                $(dragged).detach().insertBefore($firstRejected);
              }
            } else {
              if (setClass) {
                $("." + setClass)
                  .detach()
                  .insertAfter($(SEL.viewItems).last());
              } else {
                $(dragged).detach().insertAfter($(SEL.viewItems).last());
              }
            }
          }

          Counters.ajaxUpdateCounters();
          HeightScheduler.scheduleEqualHeights();
        });

      $(SEL.viewItems, context)
        .off("dragenter.hl dragover.hl dragleave.hl drop.hl")
        .on("dragenter.hl", function (e) {
          e.preventDefault();
          if (this !== dragged) $(this).addClass(CLASS.dragOver);
        })
        .on("dragover.hl", function (e) {
          e.preventDefault();
        })
        .on("dragleave.hl", function (e) {
          if (!this.contains(e.relatedTarget)) {
            $(this).removeClass(CLASS.dragOver);
          }
        })
        .on("drop.hl", function (e) {
          e.preventDefault();
          $(this).removeClass(CLASS.dragOver);
          if ($(this).hasClass("view-items-offer-rejected")) {
            const $firstRejected = $(SEL.rejectedItems).first();
            if (dragged && $firstRejected.length) {
              if (setClass) {
                $("." + setClass)
                  .detach()
                  .insertBefore($firstRejected);
              } else {
                $(dragged).detach().insertBefore($firstRejected);
              }
              Counters.ajaxUpdateCounters();
              HeightScheduler.scheduleEqualHeights();
            }
            return false;
          }
        });
    },
  };

  /********************************************************************
   * SUB OFFER EXPAND/COLLAPSE
   ********************************************************************/
  const SubOffers = {
    init(context) {
      $(`${SEL.viewItems}.${CLASS.mainOffer}`, context).each(function () {
        const $main = $(this);
        if ($main.find(`.${CLASS.subofferExpander}`).length === 0) {
          $main
            .find(".views-field-title")
            .append(
              `<div class="${CLASS.subofferExpander} ${CLASS.subofferExpand}"><span>rozwiń</span></div>`
            );
        }
        const setClass = Counters._getOfferSetClass($main);
        if (setClass) {
          $(`.view-items.${CLASS.subOffer}.${setClass}`).addClass("hidden");
        }
      });

      $(document)
        .off(
          "click.suboffersExpand",
          `.${CLASS.subofferExpander}.${CLASS.subofferExpand} span`
        )
        .on(
          "click.suboffersExpand",
          `.${CLASS.subofferExpander}.${CLASS.subofferExpand} span`,
          function () {
            SubOffers._toggle(this, true);
          }
        );

      $(document)
        .off(
          "click.suboffersCollapse",
          `.${CLASS.subofferExpander}.${CLASS.subofferCollapse} span`
        )
        .on(
          "click.suboffersCollapse",
          `.${CLASS.subofferExpander}.${CLASS.subofferCollapse} span`,
          function () {
            SubOffers._toggle(this, false);
          }
        );
    },

    _toggle(spanEl, expand) {
      const $span = $(spanEl);
      const $parent = $span.closest(".view-items");
      const setClass = Counters._getOfferSetClass($parent);
      if (!setClass) return;

      const $subs = $(`.view-items.${CLASS.subOffer}.${setClass}`);
      const $wrap = $(SEL.viewContent);
      if (expand) {
        $span
          .parent()
          .removeClass(CLASS.subofferExpand)
          .addClass(CLASS.subofferCollapse);
        $span.text("zwiń");
        $subs.removeClass("hidden").first().addClass("visible-transition");
        Utils.schedule(
          () => $subs.first().removeClass("visible-transition"),
          200
        );
      } else {
        $span
          .parent()
          .removeClass(CLASS.subofferCollapse)
          .addClass(CLASS.subofferExpand);
        $span.text("rozwiń");
        $subs.addClass("hidden").first().addClass("hidden-transition");
        Utils.schedule(
          () => $subs.first().removeClass("hidden-transition"),
          200
        );
      }

      Utils.schedule(() => {
        Drupal.behaviors.npx4growOffersEqualHeight.equalHeightRefresh();
        HeightScheduler.scheduleEqualHeights();
      }, 250);

      adjustStickyHeaders($wrap);
    },
  };

  /********************************************************************
   * HEADER WIDTH SIZER
   ********************************************************************/
  const HeaderSizer = {
    resizeHeaders() {
      document.querySelectorAll(SEL.viewContentWrapper).forEach((wrapper) => {
        const labelsCol = wrapper.querySelector(SEL.viewLabels);
        if (!labelsCol) return;
        const headers = wrapper.querySelectorAll(
          `.view-labels ${SEL.sectionHeader}`
        );
        const wrapperWidth = $(SEL.viewContentWrapper).width();
        const finalWidth = wrapperWidth + 14 + "px";
        headers.forEach((h) => (h.style.width = finalWidth));
      });
    },
  };

  /********************************************************************
   * SHARE TOGGLE
   ********************************************************************/
  const ShareToggle = {
    init(context) {
      once(
        "npx4grow-offers-share-toggle",
        ".npx-share-toggle",
        context
      ).forEach((btn) => {
        $(btn).on("click", function (e) {
          e.preventDefault();
          const $formWrapper = $(btn)
            .closest(".npx-share-footer")
            .find("#npxshare-form-wrapper");
          if ($formWrapper.hasClass(CLASS.dNone)) {
            $formWrapper.removeClass(CLASS.dNone);
            $formWrapper[0].offsetHeight;
            $formWrapper.css("height", "0").slideDown(300, function () {
              $(this).css("height", "");
              $("html, body").animate(
                { scrollTop: $formWrapper.offset().top - 100 },
                500
              );
            });
          } else {
            $formWrapper.stop(true).slideToggle(300, function () {
              if ($(this).is(":visible")) {
                $(this).css("height", "");
                $("html, body").animate(
                  { scrollTop: $formWrapper.offset().top - 100 },
                  500
                );
              }
            });
          }
        });
      });
    },
  };

  /********************************************************************
   * DATE PLACEHOLDER
   ********************************************************************/
  function datePlaceholder(context) {
    once(
      "npx4grow-offers-date-placeholder",
      'input[name="expire"]',
      context
    ).forEach((input) => {
      const $inp = $(input);
      if (!input.value) $inp.addClass("placeholder-visible");
      $inp
        .on("focus", function () {
          $inp.removeClass("placeholder-visible");
        })
        .on("blur", function () {
          if (!this.value) $inp.addClass("placeholder-visible");
        });
    });
  }

  /********************************************************************
   * EMPTY FIELD REPLACER
   ********************************************************************/
  function replaceEmptyFields(context) {
    const exclude = [
      "views-field-npx_product_offer_reviews_views_field",
      "views-field-field_offer_message_1",
      "views-field-trainer",
    ];
    $(`${SEL.offersList} .view-content .view-items .views-field`, context).each(
      function () {
        const $f = $(this);
        if (exclude.some((cls) => $f.hasClass(cls))) return true;
        if ($f.html().trim() === "") $f.html("-");
        $f.find(".field-content").each(function () {
          if ($(this).html().trim() === "") $(this).html("-");
        });
      }
    );
  }

  /********************************************************************
   * RESPONSIVE WIDTH CALC
   ********************************************************************/
  function responsiveWidths(context) {
    once("npx4grow-offer-responsive-inquiry-offers", "body", context).forEach(
      () => {
        function applyBatchClasses(divisor) {
          const k = Math.max(1, divisor - 1); // items per viewport
          const $items = $(".view-inquiry-offers-list .view-items");
          $items.removeClass(function (i, cls) {
            return cls && cls.match(/(^|\s)view-items-batch-\d+/g)
              ? cls.match(/(^|\s)view-items-batch-\d+/g).join(" ")
              : "";
          });
          const n = $items.length;
          if (n === 0) return;
          const B = Math.max(1, Math.ceil(n / k));
          const lastStart = Math.max(1, n - k + 1);
          $items.each(function (i) {
            const idx = i + 1;
            if (idx <= k) $(this).addClass("view-items-batch-1");
            for (let b = 2; b <= B - 1; b++) {
              const start = (b - 1) * k + 1;
              const end = b * k;
              if (idx >= start && idx <= end) {
                $(this).addClass(`view-items-batch-${b}`);
              }
            }
            if (idx >= lastStart) {
              $(this).addClass(`view-items-batch-${B}`);
            }
          });
        }

        function adjust() {
          const container = Utils.$1(SEL.viewContentWrapper);
          if (!container) return;
          const containerWidth = container.offsetWidth;
          let divisor = 2;
          if (window.innerWidth >= 1400) divisor = 5;
          else if (window.innerWidth >= 1200) divisor = 4;
          else if (window.innerWidth >= 992) divisor = 3;

          const itemWidth = Math.floor(containerWidth / divisor);

          const itemWidthPx = itemWidth + "px";
          container
            .querySelectorAll(".view-items, .view-labels")
            .forEach((el) => {
              el.style.width = itemWidthPx;
              el.style.maxWidth = itemWidthPx;
              el.style.flexBasis = itemWidthPx;
              el.style.minWidth = itemWidthPx;
            });

          $(SEL.viewContentWrapper).css({
            overflow: "hidden",
            scrollSnapType: "none",
          });

          applyBatchClasses(divisor);

          // Recompute active batch visibility after width/batch changes
          updateBatchClassesForViewport();

          // NEW: ensure nav arrows reflect current batch count on resize
          updateNavButtonsVisibility();
        }

        let initialBatchMarked = false;
        function markInitialBatchActiveOnce() {
          if (initialBatchMarked) return;
          const $items = $(".view-inquiry-offers-list .view-items");
          $items.removeClass("batch-active");
          $(
            ".view-inquiry-offers-list .view-items.view-items-batch-1"
          ).addClass("batch-active");
          initialBatchMarked = true;
          document.body.classList.add("batch-active-1");
          $items.not(".batch-active").css("display", "none");
          $items.filter(".batch-active").css("display", "");

          // NEW: initial arrows visibility
          updateNavButtonsVisibility();
        }

        window.addEventListener("resize", adjust);
        window.addEventListener("load", () => {
          adjust();
          markInitialBatchActiveOnce();
        });

        adjust();
      }
    );
  }

  // Recalculate batch classes based on current viewport divisor and set the active batch
  function updateBatchClassesForViewport() {
    const container = Utils.$1(SEL.viewContentWrapper);
    if (!container) return;

    let divisor = 2;
    if (window.innerWidth >= 1400) divisor = 5;
    else if (window.innerWidth >= 1200) divisor = 4;
    else if (window.innerWidth >= 992) divisor = 3;
    const k = Math.max(1, divisor - 1);

    const $items = $(".view-inquiry-offers-list .view-items");

    // Remove previous batch markers and active marker
    $items.removeClass("batch-active");
    $items.removeClass(function (i, cls) {
      return ((cls && cls.match(/(^|\s)view-items-batch-\d+/g)) || []).join(
        " "
      );
    });

    const n = $items.length;
    if (n === 0) {
      updateNavButtonsVisibility(); // keep arrows hidden if no items
      return;
    }

    const B = Math.max(1, Math.ceil(n / k));
    const lastStart = Math.max(1, n - k + 1);

    // Reassign overlapping batch classes
    $items.each(function (i) {
      const idx = i + 1;
      if (idx <= k) $(this).addClass("view-items-batch-1");
      for (let b = 2; b <= B - 1; b++) {
        const start = (b - 1) * k + 1;
        const end = b * k;
        if (idx >= start && idx <= end)
          $(this).addClass(`view-items-batch-${b}`);
      }
      if (idx >= lastStart) $(this).addClass(`view-items-batch-${B}`);
    });

    // Determine active batch without using scroll
    let activeBatch = 1;
    if (typeof updateBatchClassesForViewport._forceActive === "number") {
      activeBatch = Math.min(
        Math.max(updateBatchClassesForViewport._forceActive, 1),
        B
      );
      updateBatchClassesForViewport._forceActive = undefined;
    } else {
      const m = (document.body.className.match(/\bbatch-active-(\d+)\b/) ||
        [])[1];
      if (m) activeBatch = Math.min(Math.max(parseInt(m, 10) || 1, 1), B);
    }

    const last = updateBatchClassesForViewport._last || {
      k: null,
      activeBatch: null,
    };
    updateBatchClassesForViewport._last = { k, activeBatch };

    // Update body and active markers
    document.body.className = document.body.className.replace(
      /\bbatch-active-\d+\b/g,
      ""
    );
    document.body.classList.add(`batch-active-${activeBatch}`);

    const $active = $items.filter(`.view-items-batch-${activeBatch}`);
    $active.addClass("batch-active");

    // Hide non-active items; show active
    $items.not($active).css("display", "none");
    $active.css("display", "");

    // Unstick non-active batches (existing code)
    (function unstickNonActive() {
      $(`.view-items:not(.batch-active) ${SEL.podstawoweSelector}`).each(
        function () {
          const $el = $(this);
          if ($el.data(ATTR.isSticky)) {
            $el
              .css({
                position: "",
                top: "",
                left: "",
                width: "",
                zIndex: "",
                backgroundColor: "",
              })
              .removeData(ATTR.isSticky)
              .removeData(ATTR.originalTop)
              .removeData(ATTR.originalWidth)
              .removeData("lockedLeft");
            $el.find(SEL.sectionHeader).css("display", "");
          }
        }
      );
      const $label = $(`.view-labels ${SEL.podstawoweSelector}`);
      if ($label.length && $label.data(ATTR.isSticky)) {
        $label
          .css({
            position: "",
            top: "",
            left: "",
            width: "",
            zIndex: "",
            backgroundColor: "",
          })
          .removeData(ATTR.isSticky)
          .removeData(ATTR.originalTop)
          .removeData(ATTR.originalWidth);
        $label.find(SEL.sectionHeader).css("display", "");
      }
    })();

    // Only refresh sticky metrics if batch size or active batch changed
    const needSticky = last.k !== k || last.activeBatch !== activeBatch;
    if (
      needSticky &&
      Drupal.behaviors.npx4growOffersStickySections &&
      Drupal.behaviors.npx4growOffersStickySections
        .refreshStickySectionPositions
    ) {
      Utils.raf(
        Drupal.behaviors.npx4growOffersStickySections
          .refreshStickySectionPositions
      );
      EqualHeightsScheduler.request(80);
    }

    // NEW: update nav arrows visibility after batch recompute
    updateNavButtonsVisibility();
  }

  /********************************************************************
   * STICKY VISIBILITY TOGGLE (unchanged logic condensed)
   ********************************************************************/
  function stickyVisibility(context) {
    const $win = $(window);
    const offset = 30;
    // Use the first visible podstawowe in the active batch
    const $itemsSticky = $(
      `.view-items.batch-active ${SEL.podstawoweSelector}:visible`,
      context
    ).first();
    const $labelsSticky = $(`.view-labels ${SEL.podstawoweSelector}`, context);
    const $otherHeaders = $(
      `.view-labels > :not(${SEL.podstawoweSelector}) ${SEL.sectionHeader}, .view-labels > :not(${SEL.podstawoweSelector}) .view-label`,
      context
    );

    function update() {
      const scrollTop = $win.scrollTop();
      let itemsTrigger = 0;
      if ($itemsSticky && $itemsSticky.length) {
        itemsTrigger =
          $itemsSticky.offset().top + $itemsSticky.outerHeight() - offset;
      }
      $otherHeaders.each(function () {
        const $h = $(this);
        const top = $h.offset().top;
        if (top < itemsTrigger + 20) $h.addClass(CLASS.invisible);
        else $h.removeClass(CLASS.invisible);
      });
    }
    const onScroll = Utils.debounce(update, 16);
    $win.on("scroll.npxStickyVisibility", onScroll);
    $win.on("resize.npxStickyVisibility", Drupal.debounce(update, 100));
    update();
  }

  /********************************************************************
   * STICKY SECTIONS (original logic reused)
   ********************************************************************/
  let viewItemPositions = [];
  const StickySections = {
    calculateLeftPosition($el) {
      const match = $el.attr("class").match(/position(\d+)/);
      if (!match) return 0;
      const position = parseInt(match[1], 10);
      // Use only currently active (visible) items for left calculations
      const $viewItems = $(`${SEL.viewItems}.batch-active`);
      if (position > 0 && position <= $viewItems.length) {
        const $target = $viewItems.eq(position - 1);
        if ($target.length && $target.offset()) {
          return $target.offset().left;
        }
      }
      const $parent = $el.closest(".view-items");
      if ($parent.length && $parent.offset()) {
        return $parent.offset().left - $parent.width();
      }
      return 0;
    },

    // NEW: only adjust left of elements that are already sticky
    realignStickyLefts() {
      // Item columns
      $(`.view-items ${SEL.podstawoweSelector}`).each(function () {
        const $s = $(this);
        if ($s.data(ATTR.isSticky)) {
          const left = StickySections.calculateLeftPosition($s);
          if (Number.isFinite(left)) {
            $s.css("left", left + "px");
          }
        }
      });
      // Labels column
      const $label = $(`.view-labels > ${SEL.podstawoweSelector}`);
      if ($label.length && $label.data(ATTR.isSticky)) {
        const lLeft = $(SEL.offersList).offset().left;
        if (Number.isFinite(lLeft)) {
          $label.css("left", lLeft + "px");
        }
      }
    },

    stickySections() {
      const $win = $(window);
      const $viewContainer = $(SEL.offersList);
      const headerH = $("header #sticky-header-top").outerHeight() || 0;
      const scrollTop = $win.scrollTop();
      // SAFE footer clamp
      const $footer = $(SEL.shareFooter);
      const footerTop = $footer.length
        ? $footer.offset().top - 300
        : Number.POSITIVE_INFINITY;

      // Only for labels and active batch items
      const $stickyItems = $(
        `.view-items.batch-active ${SEL.podstawoweSelector}`
      );
      const $labelBlock = $(`.view-labels > ${SEL.podstawoweSelector}`);

      let anySticky = false;
      let maxStickyHeight = 0;
      let stateChanged = false;

      $stickyItems.each(function () {
        const $section = $(this);
        const wasSticky = !!$section.data(ATTR.isSticky);
        if (!wasSticky) {
          $section.data(ATTR.originalTop, $section.offset().top);
          $section.data(ATTR.originalWidth, $section.outerWidth());
        }

        const origTop = $section.data(ATTR.originalTop);
        const h = $section.outerHeight();
        const w = $section.data(ATTR.originalWidth);
        const $hdr = $section.find(SEL.sectionHeader);

        const shouldStick =
          scrollTop + headerH > origTop &&
          scrollTop + headerH + h + 100 < footerTop;

        if (shouldStick && !wasSticky) {
          $("body").addClass(CLASS.stickyActiveBody);
          const left = StickySections.calculateLeftPosition($section); // no lockedLeft
          $section.css({
            position: "fixed",
            top: headerH + "px",
            left: left + "px",
            width: w + "px",
            zIndex: 10,
            backgroundColor: "white",
          });
          $hdr.css("display", "none");
          $section.data(ATTR.isSticky, true);
          stateChanged = true;
        } else if (!shouldStick && wasSticky) {
          $section
            .css({
              position: "",
              top: "",
              left: "",
              width: "",
              zIndex: "",
              backgroundColor: "",
            })
            .removeData(ATTR.isSticky);
          $hdr.css("display", "");
          stateChanged = true;
        } else if (shouldStick && wasSticky) {
          const left = StickySections.calculateLeftPosition($section); // recompute every pass
          $section.css("left", left + "px");
        }

        if (shouldStick) {
          anySticky = true;
          maxStickyHeight = Math.max(maxStickyHeight, h);
        }
      });

      if ($labelBlock.length) {
        const labelWasSticky = !!$labelBlock.data(ATTR.isSticky);
        if (!labelWasSticky) {
          $labelBlock.data(ATTR.originalTop, $labelBlock.offset().top);
          $labelBlock.data(ATTR.originalWidth, $labelBlock.outerWidth());
        }
        const oTop = $labelBlock.data(ATTR.originalTop);
        const h = $labelBlock.outerHeight();
        const w = $labelBlock.data(ATTR.originalWidth);
        const lLeft = $viewContainer.offset().left;
        const $hdr = $labelBlock.find(SEL.sectionHeader);
        const shouldStick =
          scrollTop + headerH > oTop &&
          scrollTop + headerH + h + 100 < footerTop;

        if (shouldStick && !labelWasSticky) {
          $labelBlock.css({
            position: "fixed",
            top: headerH + "px",
            left: lLeft + "px",
            width: w + "px",
            zIndex: 10,
            backgroundColor: "white",
          });
          $hdr.css("display", "none");
          $labelBlock.data(ATTR.isSticky, true);
          stateChanged = true;
        } else if (!shouldStick && labelWasSticky) {
          $labelBlock
            .css({
              position: "",
              top: "",
              left: "",
              width: "",
              zIndex: "",
              backgroundColor: "",
            })
            .removeData(ATTR.isSticky);
          $hdr.css("display", "");
          stateChanged = true;
        }
      }

      if (anySticky) {
        $(`${SEL.offersList} .view-content-wrapper`).css(
          "padding-top",
          maxStickyHeight + "px"
        );
      } else {
        $("body").removeClass(CLASS.stickyActiveBody);
        $(`${SEL.offersList} .view-content-wrapper`).css("padding-top", "0");
      }

      updateNavButtonsStickyState(anySticky);

      if (stateChanged) {
        if (anySticky) {
          EqualHeights.equalizeCompanyFieldHeights();
        } else {
          equalizeCompanyFieldHeightAfterSticky();
        }
        // Debounced equal-height refresh to prevent jumpiness
        EqualHeightsScheduler.request(60);
      }

      updateStickyHeaderVisibility();
    },
  };

  function adjustStickyHeaders($viewContent) {
    Utils.schedule(() => setLeftAndHideItems($viewContent), 100);
    setLeftAndHideItems($viewContent);
  }

  // Stub of original (left/hide items). Provide original implementation if needed.
  function setLeftAndHideItems() {
    // Intentionally left as a hook (original code referenced but not present in supplied snippet)
  }

  /********************************************************************
   * POSITION CLASSES
   ********************************************************************/
  const PositionClasses = {
    assignPositionClasses() {
      // Clear existing position classes from all
      $(`.view-items ${SEL.podstawoweSelector}`).removeClass(function (i, c) {
        return (c.match(/(^|\s)position\d+/g) || []).join(" ");
      });
      // Assign sequential positions only for visible (active batch) items
      $(`${SEL.viewItems}.batch-active`).each(function (idx) {
        $(this)
          .find(SEL.podstawoweSelector)
          .addClass(`${CLASS.positionPrefix}${idx + 1}`);
      });
    },
  };

  /********************************************************************
   * REMAINING SMALL HELPERS
   ********************************************************************/
  function afterAction() {
    const $vc = $(`${SEL.offersList} .view-content`);
    toggleOpacity($vc);
  }
  function toggleOpacity() {
    // Original placeholder for visual feedback effect
  }

  function equalizeCompanyFieldHeightAfterSticky() {
    // Use existing original variant - reused from original file (unchanged)
    $(".views-field-npx_offer_profile_company_views_field").css("height", "");
    let maxHeight = 0;
    $(".views-field-npx_offer_profile_company_views_field").each(function () {
      maxHeight = Math.max(maxHeight, $(this).innerHeight());
    });
    if (maxHeight > 0) {
      $(".views-field-npx_offer_profile_company_views_field").css(
        "height",
        maxHeight + 10 + "px"
      );
    }
    Utils.schedule(() => {
      $(".views-field-npx_offer_actions_views_field").css("height", "");
      let maxTotal = 0;
      $(".views-field-npx_offer_actions_views_field").each(function () {
        let h = 0;
        $(this)
          .children(":visible")
          .each(function () {
            h += $(this).outerHeight(true);
          });
        if (h > 0) maxTotal = Math.max(maxTotal, h);
      });
      maxTotal += 5;
      if (maxTotal > 0) {
        $(".views-field-npx_offer_actions_views_field").css(
          "height",
          maxTotal + "px"
        );
      }
    }, 50);

    let totalHeight = 0;
    Utils.schedule(() => {
      $(`.view-labels ${SEL.podstawoweSelector}.section-block`).each(
        function () {
          $(this).css("height", "");
          $(this)
            .find(".view-label")
            .each(function () {
              totalHeight += $(this).outerHeight(true);
            });
        }
      );
      if (totalHeight > 0) {
        totalHeight += 20;
        $(`${SEL.podstawoweSelector}.section-block`).css(
          "height",
          totalHeight + "px"
        );
        $(`${SEL.podstawoweSelector}.section-block ${SEL.sectionContent}`).css(
          "height",
          totalHeight + "px"
        );
      }
    }, 100);
  }

  // NEW: visibility controller for headers relative to sticky podstawowe block.
  function updateStickyHeaderVisibility() {
    const bodySticky = document.body.classList.contains(CLASS.stickyActiveBody);
    const ref = document.querySelector(
      `.view-labels > ${SEL.podstawoweSelector}`
    );
    const headers = document.querySelectorAll(SEL.sectionHeader);

    // If no sticky mode or no reference, restore visibility.
    if (!bodySticky || !ref) {
      headers.forEach((h) => {
        if (h.style.visibility === "hidden") h.style.visibility = "visible";
      });
      return;
    }

    const threshold = ref.getBoundingClientRect().bottom + 5;

    headers.forEach((h) => {
      // Skip headers that are display:none (e.g. hidden podstawowe header while sticky).
      if (h.offsetParent === null && h.style.display === "none") return;
      const top = h.getBoundingClientRect().top;
      if (top < threshold) {
        if (h.style.visibility !== "hidden") h.style.visibility = "hidden";
      } else {
        if (h.style.visibility !== "visible") h.style.visibility = "visible";
      }
    });
  }

  // Add this helper to toggle nav buttons when sticky mode changes.
  function updateNavButtonsStickyState(isSticky) {
    const $leftBtn = $(".npx-nav-btn.npx-nav-btn--left");
    const $rightBtn = $(".npx-nav-btn.npx-nav-btn--right");
    const $container = $(SEL.offersList).first();
    if (!$leftBtn.length && !$rightBtn.length) return;

    if (isSticky && $container.length) {
      const rect = $container[0].getBoundingClientRect();
      const vw = window.innerWidth;

      // Switch to fixed and set top.
      $leftBtn
        .removeClass("position-absolute")
        .addClass("position-fixed")
        .css({
          top: "105px",
          left: `${rect.left - 20}px`,
          right: "",
        });

      // For the right arrow, use 'right' relative to viewport.
      const rightOffset = Math.max(0, vw - rect.right - 20);
      const stylesRight = {
        top: "105px",
        right: `${rightOffset}px`,
        left: "",
      };
      $rightBtn
        .removeClass("position-absolute")
        .addClass("position-fixed")
        .css(stylesRight);
    } else {
      // Revert to absolute and clear inline positioning.
      const reset = { top: "", left: "", right: "" };
      $leftBtn
        .removeClass("position-fixed")
        .addClass("position-absolute")
        .css(reset);
      $rightBtn
        .removeClass("position-fixed")
        .addClass("position-absolute")
        .css(reset);
    }
  }

  // NEW: show/hide nav buttons based on batch count and current batch.
  function updateNavButtonsVisibility() {
    const $container = $(SEL.offersList).first();
    if (!$container.length) return;

    const $left = $container.find(".npx-nav-btn--left");
    const $right = $container.find(".npx-nav-btn--right");

    if (!$left.length && !$right.length) return;

    // Compute current divisor -> items per batch (k)
    let divisor = 2;
    if (window.innerWidth >= 1400) divisor = 5;
    else if (window.innerWidth >= 1200) divisor = 4;
    else if (window.innerWidth >= 992) divisor = 3;
    const k = Math.max(1, divisor - 1);

    const n = $container.find(".view-items").length;
    const B = Math.max(1, Math.ceil(n / k));

    // Find active batch from body class
    let current = 1;
    const m = (document.body.className.match(/\bbatch-active-(\d+)\b/) ||
      [])[1];
    if (m) current = Math.min(Math.max(parseInt(m, 10) || 1, 1), B);

    // Right button visible only if there is a next batch
    if (B > 1 && current < B) $right.removeClass("d-none");
    else $right.addClass("d-none");

    // Left button visible only if not on the first batch
    if (current > 1) $left.removeClass("d-none");
    else $left.addClass("d-none");
  }

  /********************************************************************
   * DRUPAL BEHAVIORS (exports)
   ********************************************************************/
  Drupal.behaviors.npx4growOffersSections = {
    attach(context) {
      SectionsToggle.init(context);
    },
  };

  Drupal.behaviors.npx4growOffersAddingClassesToOfferList = {
    attach(context) {
      groupOfferSets(context);
    },
  };

  Drupal.behaviors.npx4growOffersEqualHeight = {
    attach() {
      EqualHeights.equalHeightRefresh();
      Utils.schedule(() => {
        $(".views-field-field-offer-trainers-list, .view-label").css({
          height: "auto",
          "min-height": "auto",
        });
        EqualHeights.equalHeightRefresh();
      }, 500);
      Utils.schedule(EqualHeights.equalHeightRefresh, 1000);

      if (
        once(
          "npx-4grow-offers-equal-height-scroll",
          document.querySelectorAll(SEL.offersList)
        ).length
      ) {
        Utils.schedule(() => {
          $(SEL.offersList).removeClass("n-loading");
          window.scrollTo({ top: 0, behavior: "smooth" });
        }, 1150);
      }

      $('.npx-offer-filters input[type="checkbox"]').on("click", () =>
        Utils.schedule(EqualHeights.equalHeightRefresh, 20)
      );
    },
    equalHeightRefresh: EqualHeights.equalHeightRefresh,
    equalizeCompanyFieldHeights: EqualHeights.equalizeCompanyFieldHeights,
  };

  Drupal.behaviors.npx4growOffersColorboxInit = {
    attach(context) {
      initializeColorbox(context);
    },
  };

  Drupal.behaviors.npx4growOffersTrimTrainerBodyAndShowMore = {
    attach(context) {
      TrainerBody.trim(context);
      TrainerBody.showMore(context);
    },
  };

  Drupal.behaviors.npx4growOffersReplaceSpanWithInput = {
    attach(context) {
      replaceSpanWithInput(context);
    },
  };

  Drupal.behaviors.npx4growOffersChangingCounters = {
    attach(context) {
      if ($("body").hasClass("user-not-logged-in")) return;
      Counters.initMoveButtons(context);
      Counters.initCounterInputs(context);
      Counters.initDragAndDrop(context);
    },
    ajaxUpdateCounters: Counters.ajaxUpdateCounters,
    moveToLastPosition: Counters.moveToLastPosition,
    moveToFirstPosition: Counters.moveToFirstPosition,
  };

  Drupal.behaviors.npx4growOffersExpandSuboffers = {
    attach(context) {
      SubOffers.init(context);
    },
  };

  Drupal.behaviors.npx4growOffersHeaderSizer = {
    attach(context) {
      once(
        "npx-4grow-offers-header-sizer",
        `${SEL.offersList} .view-content-wrapper`,
        context
      );
      HeaderSizer.resizeHeaders();
      window.addEventListener(
        "resize",
        Utils.debounce(HeaderSizer.resizeHeaders, 120)
      );
    },
    resizeHeaders: HeaderSizer.resizeHeaders,
    npxOnResize: HeaderSizer.resizeHeaders,
  };

  Drupal.behaviors.npx4growOffersPagerKeyboard = {
    attach(context) {
      once(
        "npx4grow-offers-pager-keyboard",
        ".view-content-wrapper",
        context
      ).forEach((wrapper) => {
        wrapper.tabIndex = 0;
        wrapper.style.outline = "none";
        wrapper.addEventListener("mouseenter", () => wrapper.focus());
      });
    },
  };

  Drupal.behaviors.npx4growOffersShareToggle = {
    attach(context) {
      ShareToggle.init(context);
    },
  };

  Drupal.behaviors.npx4growOffersDateFieldPlaceholder = {
    attach(context) {
      datePlaceholder(context);
    },
  };

  Drupal.behaviors.npxReplaceEmptyFields = {
    attach(context) {
      replaceEmptyFields(context);
    },
  };

  Drupal.behaviors.npx4growOfferresponsiveInquiryOffers = {
    attach(context) {
      responsiveWidths(context);
    },
    // Expose for reuse after drag/drop or moves.
    recomputeBatches: updateBatchClassesForViewport,
  };

  Drupal.behaviors.npx4growStickyVisibilityToggle = {
    attach(context) {
      stickyVisibility(context);
    },
    detach() {
      $(window).off("scroll.npxStickyVisibility resize.npxSticky");
    },
  };

  Drupal.behaviors.npx4growOffersStickySections = {
    attach(context) {
      const $win = $(window);
      $(SEL.viewItems, context).each(function (idx) {
        viewItemPositions[idx + 1] = $(this).offset().left;
      });
      // Prepare sticky metrics only for active batch items
      $(`.view-items.batch-active ${SEL.podstawoweSelector}`, context).each(
        function () {
          const $s = $(this);
          if (!$s.data(ATTR.originalTop)) {
            $s.data(ATTR.originalTop, $s.offset().top);
            $s.data(ATTR.originalWidth, $s.outerWidth());
          }
        }
      );
      const $labelBlock = $(
        `.view-labels > ${SEL.podstawoweSelector}`,
        context
      );
      if ($labelBlock.length && !$labelBlock.data(ATTR.originalTop)) {
        $labelBlock.data(ATTR.originalTop, $labelBlock.offset().top);
        $labelBlock.data(ATTR.originalWidth, $labelBlock.outerWidth());
      }
      Utils.raf(StickySections.stickySections);
      $win
        .off("scroll.npxSticky resize.npxSticky")
        .on(
          "scroll.npxSticky resize.npxSticky",
          Drupal.debounce(StickySections.stickySections, 50)
        );
    },
    stickySections: StickySections.stickySections,
    refreshStickySectionPositions() {
      const $wrapper = $(".view-content-wrapper");
      const scrollBefore = $wrapper.scrollLeft();
      $wrapper.scrollLeft(0);
      viewItemPositions = [];
      $(".view-content-wrapper .view-items").each(function (idx) {
        viewItemPositions[idx + 1] = $(this).offset().left;
      });
      // Refresh metrics only for active batch items
      $wrapper
        .find(".view-items.batch-active " + SEL.podstawoweSelector)
        .each(function () {
          const $s = $(this);
          $s.data(ATTR.originalTop, $s.offset().top);
          $s.data(ATTR.originalWidth, $s.outerWidth());
        });

      // NEW: refresh label column metrics too (they can change across batches)
      const $labelBlock = $(`.view-labels > ${SEL.podstawoweSelector}`);
      if ($labelBlock.length) {
        $labelBlock.data(ATTR.originalTop, $labelBlock.offset().top);
        $labelBlock.data(ATTR.originalWidth, $labelBlock.outerWidth());
      }

      StickySections.stickySections();
      $wrapper.scrollLeft(scrollBefore);
    },
  };

  Drupal.behaviors.npx4growOffersTrainerBgBlue = {
    attach(context) {
      $(`${SEL.offersList} .view-items`, context).each(function () {
        $(this)
          .find(".views-field-trainer.paragraph--type--trainer-with-video")
          .each(function (i) {
            if ((i + 1) % 2 === 0) {
              $(this).addClass("color-bg-light-blue");
            } else {
              $(this).removeClass("color-bg-light-blue");
            }
          });
      });
    },
  };

  /********************************************************************
   * BATCH NAVIGATION (NEW)
   ********************************************************************/
  Drupal.behaviors.npx4growOffersBatchNavigation = {
    attach(context) {
      once("npx4grow-offers-batch-nav", "body", context).forEach(() => {
        function computeDivisor() {
          if (window.innerWidth >= 1400) return 5;
          if (window.innerWidth >= 1200) return 4;
          if (window.innerWidth >= 992) return 3;
          return 2;
        }

        function getBatchInfo() {
          const divisor = computeDivisor();
          const k = Math.max(1, divisor - 1);
          const n = document.querySelectorAll(
            ".view-inquiry-offers-list .view-items"
          ).length;
          const B = Math.max(1, Math.ceil(n / k));

          let current = 1;
          const m = (document.body.className.match(/\bbatch-active-(\d+)\b/) ||
            [])[1];
          if (m) current = Math.min(Math.max(parseInt(m, 10) || 1, 1), B);
          else {
            const $active = $(
              ".view-inquiry-offers-list .view-items.batch-active"
            ).first();
            if ($active.length) {
              const cls = ($active.attr("class") || "").match(
                /view-items-batch-(\d+)/
              );
              if (cls)
                current = Math.min(Math.max(parseInt(cls[1], 10) || 1, 1), B);
            }
          }

          return { k, n, B, current };
        }

        function gotoBatch(targetBatch) {
          const info = getBatchInfo();
          if (!info) return;
          const { B } = info;
          const clamped = Math.min(Math.max(targetBatch, 1), B);

          updateBatchClassesForViewport._forceActive = clamped;
          updateBatchClassesForViewport();

          if (
            Drupal.behaviors.npx4growOffersPositionClasses &&
            Drupal.behaviors.npx4growOffersPositionClasses.assignPositionClasses
          ) {
            Drupal.behaviors.npx4growOffersPositionClasses.assignPositionClasses();
          }

          // Settle layout, then refresh metrics and stickiness robustly.
          if (
            Drupal.behaviors.npx4growOffersStickySections &&
            Drupal.behaviors.npx4growOffersStickySections
              .refreshStickySectionPositions
          ) {
            Utils.raf(() => {
              Drupal.behaviors.npx4growOffersStickySections.refreshStickySectionPositions();
              Utils.schedule(() => {
                Drupal.behaviors.npx4growOffersStickySections.refreshStickySectionPositions();
                if (
                  Drupal.behaviors.npx4growOffersStickySections.stickySections
                ) {
                  Drupal.behaviors.npx4growOffersStickySections.stickySections();
                }
                StickySections.realignStickyLefts();
              }, 60);
              Utils.schedule(() => {
                if (
                  Drupal.behaviors.npx4growOffersStickySections.stickySections
                ) {
                  Drupal.behaviors.npx4growOffersStickySections.stickySections();
                }
                StickySections.realignStickyLefts();
              }, 160);
            });
          }

          EqualHeightsScheduler.request(80);

          // NEW: refresh nav arrows right after switching batch
          updateNavButtonsVisibility();
        }

        // Remove any scroll-based sync (no scroller used)
        const scroller = Utils.$1(SEL.viewContentWrapper);
        if (scroller) {
          scroller.removeEventListener?.(
            "scroll",
            updateBatchClassesForViewport
          );
        }

        $(document)
          .off("click.npxBatchNavRight")
          .on("click.npxBatchNavRight", ".npx-nav-btn--right", function (e) {
            e.preventDefault();
            const info = getBatchInfo();
            if (!info) return;
            const next = Math.min(info.current + 1, info.B);
            if (next !== info.current) gotoBatch(next);
          });

        $(document)
          .off("click.npxBatchNavLeft")
          .on("click.npxBatchNavLeft", ".npx-nav-btn--left", function (e) {
            e.preventDefault();
            const info = getBatchInfo();
            if (!info) return;
            const prev = Math.max(info.current - 1, 1);
            if (prev !== info.current) gotoBatch(prev);
          });

        // Initial visibility + resize updates
        updateNavButtonsVisibility();
        window.addEventListener(
          "resize",
          Drupal.debounce(updateNavButtonsVisibility, 120)
        );
      });
    },
  };

  Drupal.behaviors.npx4growOffersPositionClasses = {
    attach() {
      PositionClasses.assignPositionClasses();
    },
    assignPositionClasses: PositionClasses.assignPositionClasses,
    swapPositionsPreservingLocation(oldPos, newPos) {
      // Keep original advanced swap logic if needed later
      PositionClasses.assignPositionClasses();
      // Additional preserving can be re-added—omitted for conciseness
      Utils.schedule(() => {
        if (Drupal.behaviors.npx4growOffersStickySections.stickySections) {
          Utils.raf(
            Drupal.behaviors.npx4growOffersStickySections.stickySections
          );
        }
      }, 250);
      return true;
    },
    swapPositionClasses(oldPos, newPos) {
      const $old = $(
        `.view-items ${SEL.podstawoweSelector}.${CLASS.positionPrefix}${oldPos}`
      );
      const $new = $(
        `.view-items ${SEL.podstawoweSelector}.${CLASS.positionPrefix}${newPos}`
      );
      if ($old.length && $new.length) {
        $old
          .removeClass(`${CLASS.positionPrefix}${oldPos}`)
          .addClass(`${CLASS.positionPrefix}${newPos}`);
        $new
          .removeClass(`${CLASS.positionPrefix}${newPos}`)
          .addClass(`${CLASS.positionPrefix}${oldPos}`);
        return true;
      }
      return false;
    },
  };

  /********************************************************************
   * GLOBAL HOOK
   ********************************************************************/
  window.npx4growRefreshUI = function () {
    Drupal.behaviors.npx4growOffersStickySections.refreshStickySectionPositions();
  };

  // Add after utilities (or anywhere top-level before usage)
  function realignScrollWrapper() {
    const wrapper = document.querySelector(".view-scroll-wrapper");
    if (!wrapper) return;

    const labels = document.querySelector(
      ".view-inquiry-offers-list .view-labels"
    );
    const gutter = 0; // keep 0 to align exactly next to labels

    let offset = 0;
    if (labels) {
      const w = labels.getBoundingClientRect().width || labels.offsetWidth || 0;
      offset = Math.round(w) + gutter;
    } else {
      // Fallback: one card width if labels are not present
      const card = document.querySelector(
        ".view-inquiry-offers-list .view-items"
      );
      if (card) {
        const w = card.getBoundingClientRect().width || card.offsetWidth || 0;
        offset = Math.round(w) + gutter;
      }
    }

    // Clear fragile inline positioning and rely on flow + margin
    wrapper.style.left = "";
    wrapper.style.position = "";
    wrapper.style.marginLeft = offset ? offset + "px" : "";
  }
})(jQuery, Drupal, drupalSettings, window, document);
