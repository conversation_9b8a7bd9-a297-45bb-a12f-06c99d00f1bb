.npx-nav-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: absolute;
  top: 327px;
  z-index: 99999;

  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
    transform: scale(1.05);
  }

  &.npx-nav-btn--left {
    left: -15px;
  }

  &.npx-nav-btn--right {
    right: -15px;
  }

  &__icon {
    width: 22px;           /* slightly larger */
    height: 22px;
    stroke: #0a2b6c;       /* navy */
    stroke-width: 2.8;     /* balanced thickness */
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
  }
}
