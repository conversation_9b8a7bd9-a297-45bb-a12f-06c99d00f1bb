.webform-submission-add-form.webform-details-toggle, .npx-ask-form {
  max-width: 930px;
  margin-bottom: 3rem;
  .js-form-type-processed-text {
    color: $color-primary;
    ul {
      list-style: circle;
    }
  }
  & > .form-item, .fieldset-wrapper > .form-item {
    margin: 1em 0;
  }
  input.form-text {
    padding: 0.75rem;
  }
  .js-form-type-checkbox {
    margin-top: 0;
    margin-left: 0;
    label {
      top: -2px;
      position: relative;
    }
  }
  textarea {
    max-width: 100%;
    width: 100%;
  }
  select {
    color: #6c757d;
  }
}
.webform-type-webform-address {
  margin-bottom: 0;
  legend {
    font-size: 16px;
    font-weight: 500;
  }
}
.webform-confirmation h2 {
  color: $color-primary;
}
#captcha {
  font-size: 14px;
  margin-bottom: 1rem;
  summary {
    display: none;
  }
}
.webform-likert-table {
  tbody tr {
    background: rgba(229, 229, 229, 0.6);
    border-top: solid 5px #fff;
  }
  th {
    padding-bottom: 8px;
  }
  td {
    padding: 8px;
    padding-top: 12px;
    .label {
      margin-top: -2px;
    }
  }
}

.webform-type-fieldset {
  .fieldset-legend {
    @include h3OnPages;
  }
}
.webform-submission-form {
  .js-form-type-radio, .js-form-type-checkbox {
    .field {
      display: flex;
      input {
        min-width: 16px;
        margin-right: 3px;
        top: 3px;
      }
    }
  }
  .js-form-type-textfield, .js-form-type-managed-file,
  .js-form-type-select, .js-form-type-textarea,
  .js-form-type-webform-likert, .js-form-type-email {
    & > .label > label {
      width: 100%;
      padding: 0;
      margin-bottom: .5rem;
      font-size: calc(1.275rem + .3vw);
      line-height: inherit;
      @include media-breakpoint-up(xl) {
        font-size: 1.5rem;
      }
    }
  }
  &.webform-details-toggle > .form-item {
    margin-top: 2rem;
    &:first-of-type {
      margin-top: 2.75rem;
    }
  }
}
.page-entity-webform-canonical {
  .form-radio.form-control[type="radio"],
  .form-checkbox.form-control[type="checkbox"] {
    margin-left: 26px;
  }
}
