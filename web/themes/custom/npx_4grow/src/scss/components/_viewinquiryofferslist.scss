@import 'components/trustedpartner';
@import 'components/businesspartner';
@import 'components/paragraphreview';
@import 'components/googlerank';
@import 'components/trainerteaser2';
@import "components/slidercomponent";
@import 'components/steps';
@import 'components/offerssection';
@import 'components/npxshare';
@import 'components/toastmessagesmall';
@import 'components/ajaxprogressfullscreen';
@import 'components/npxnavbuttons';
@import 'components/icons/listnoyes';

.view-items.offer-withdrawn {
  .views-field .field-content {
    opacity: 0.5;
  }
}
.views-field-npx_offer_profile_company_views_field {
  position: relative;
}
.view-items:not(.batch-active) {
  display: none;
}
.view-items.npx-sub-offer {
  width: 327px;
  &.hidden {
    .views-field {
      max-height: 20px;
      height: 20px;
      overflow-y: hidden;
    }
    opacity: 0;
    width: 0;
    min-width: 0;
    max-width: 0;
    pointer-events: none;
    &.hidden-transition {
      transform: translateX(-100%);
      will-change: opacity width min-width max-width;
      transition: transform .5s ease-in, opacity .25s ease-in, width .5s ease-in, min-width .5s ease-in, max-width .5s ease-in;
    }
  }
  &.visible-transition {
    will-change: opacity width min-width max-width;
    transition: transform 0.5s ease, opacity 0.5s ease, width 0.5s ease;
    transform: translateX(0);
  }
}
.npx-main-offer {
  .views-field-title {
    position: relative;
  }
  .npx-suboffer-expander {
    position: absolute;
    right: -1px;
    width: 100px;
    height: 60px;
    bottom: -1px;
    padding-bottom: 5px;
    background-image: url('../src/images/border-expand.png');
    color: red;
    &.npx-suboffer-collapse {
      right: unset;
      left: -1px;
      background-image: url('../src/images/border-close.png');
      span {
        color: #3f48cc;
        right: unset;
        left: 8px;
      }
    }
    span {
      cursor: pointer;
      bottom: 1px;
      right: 4px;
      position: absolute;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}
.view-inquiry-offers-list {
  .dropdown-item-text {
    padding-top: 0;
    padding-bottom: 0;
  }
  .paragraph--type--offer-review {
    max-width: 90%;
    margin: 0 auto;
    @include media-breakpoint-up(xxl) {
      @include media-breakpoint-down(xxxl) {
        max-width: 78%;
      }
    }
  }
  .slider-container {
    .next {
      right: -40px;
    }
  }
  .views-exposed-form {
    .form-items-group {
      display: inline-flex;
    }
    .js-form-type-checkbox {
      margin-left: 0;
    }
    input.form-submit {
      margin-top: -12px;
      margin-bottom: 10px;
      margin-left: 8px;
      width: auto;
      display: inline;
    }
  }
  & > .view-content {
    border: 1px solid #fff;
    margin-left: -5px;
    margin-right: -5px;
    background: #fff;
    padding-left: 5px;
    padding-right: 5px;
  }
  .view-labels {
    & > .view-label:last-of-type {
      border-top-left-radius: $form-border-radius;
    }
    & > .view-label:nth-last-of-type(2) {
      border-bottom-left-radius: $form-border-radius;
    }
  }
  .view-items:last-of-type {
    & > .views-field-nothing-1 {
      border-top-right-radius: $form-border-radius;
    }
  }
  .views-field-field-offer-message-1 {
    .field-content {
      padding: 5px;
    }
    text-align: left;
  }
  .views-field .views-label {
    display: none;
  }
  .view-content {
    overflow-x: auto;
    padding-bottom: 1.5rem;
  }
  .section-block__content > div {
    &.views-field-npx_offer_actions_views_field {
      padding-bottom: 0;
    }
    padding: 8px;
    &.offer-message-comment-link-append {
      padding-top: 0;
    }
  }
  .view-labels {
    border-top-left-radius: $form-border-radius;
    border-bottom-left-radius: $form-border-radius;
    flex-shrink: 0;
    background: white;
    width: 327px;
   .section-block__content > div {
      padding: 8px 4px;
    }
  }
  .view-items {
    display: flex;
    position: relative;
    max-width: 400px;
    min-width: 400px;
    border-top-right-radius: $form-border-radius;
    border-bottom-right-radius: $form-border-radius;
    @include media-breakpoint-up(sm) {
      min-width: 327px;
      max-width: 327px;
    }
    &:last-of-type .move-to-next,
    &:first-of-type .move-to-prev {
      display: none;
    }
    &.drag-over:not(.view-items-offer-rejected) {
      border: 1px solid $blue--light;
    }
    /* view-items before the .view-items-offer-rejected class */
    &:has(+ .view-items-offer-rejected) {
      &:hover {
        border-right-color: transparent;
        .views-field-field_offer_inquiry_counter .move-to-next {
          display: none;
        }
      }
    }
  }
  .views-field-field_benefit2_flag, .views-field-field_benefit3_flag {
    position: relative;
    .list-no {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
.page-node-npx_inquiry .view-inquiry-offers-list article {
  padding-bottom: 0.25rem;
}
.form-item-comment textarea {
  width: 100%;
  border-top-left-radius: $new-form-border-radius;
  border-top-right-radius: $new-form-border-radius;
  border-color: $new-border-color;
  &:focus-visible {
    outline: none;
  }
}
.views-field-offer_comment_edit {
  .form-submit {
    @include submit-button-new;
    border: $new-border-color 1px solid;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: $new-form-border-radius;
    border-bottom-right-radius: $new-form-border-radius;
    background-color: #fff;
    color: $color-primary;
    margin-top: -4px;
    @include t3b;
    &:hover, &:focus, &:active {
      background-color: $color-bg-light-blue;
      color: $color-primary;
      box-shadow: none;
    }
  }
}
.field-business-partner, .field-trusted-partner {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
}
.views-field-npx-offer-profile-company-views-field .field-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.npx-company-title-logo {
  display: block;
  font-weight: 700;
  color: #02004C;
  font-size: 0.8rem;
  line-height: $line-height-sm; 
}
.paragraph--type--trainer-with-video iframe {
  max-width: 100%;
  height: auto;
}
.views-field-field-offer-inquiry-counter {
  .field-content {
    display: flex;
    justify-content: space-around;
  }
}
.page-npx-offer-npx-offer-list-controller-get-offer-list {
  .breadcrumb {
    margin-bottom: 0;
  }
  #main-content {
    margin-top: 0;
    #content {
      padding-top: 0;
      padding-bottom: 0;
    }
  }
  /** to change later! */
  h3, .h3 {
    @include h3New;
  }
}
.view-inquiry-offers-list {
  .t2, .t2b {
    font-size: 14px;
    line-height: 1.15;
  }
  // ── turn the wrapper into a horizontal grid ─────────────────────────
  .view-content-wrapper {
    overflow-x: hidden;          /* scroll if there are too many columns */
    align-items: start;        /* align all cells to the top */
  }

  // ── “peel away” the scroll wrapper so each .view-items becomes a grid-cell
  .view-scroll-wrapper {
    display: flex;
    width: max-content;
    max-width: max-content;
    &:after {
      content: "";
      display: block;
      width: 1px;
    }
  }

  // ── each offer column is now a true grid cell ───────────────────────
  .view-items {
    display: flex;             /* you can still flex the contents vertically */
    flex-direction: column;
    text-align: center;
    background: #fff;
    flex-grow: 0;
    flex-shrink: 0;
    border: 1px solid transparent;
    &:last-of-type {
      border-top-right-radius: $form-border-radius;
      border-bottom-right-radius: $form-border-radius;
    }
    &:not(.view-items-offer-rejected) {
      &:hover, &:focus, &:active {
        background-color: $color-bg-light-blue;
        border: $new-border-separator;
        border-top-color: transparent;
        .views-field-field_offer_inquiry_counter {
          .move-to-next, .move-to-prev {
            visibility: visible;
          }
        }
        .section-block--informacje-podstawowe .section-block__content,
        .section-block--informacje-podstawowe {
          background-color: $color-bg-light-blue!important;
        }
      }
    }
    &.view-items-offer-rejected {
      background-color: $new-gray-100;
      border: $new-border-separator;
      border-top-color: transparent;
      &:not(:last-of-type) {
        border-right-color: transparent;
      }
      .color-bg-light-blue {
        background-color: $new-gray-100;
      }
      .section-block--informacje-podstawowe .section-block__content {
        background-color: $new-gray-100!important;
      }
      .section-block--informacje-podstawowe {
        background-color: $new-gray-100!important;
      }
      .views-field-field_offer_inquiry_counter {
        opacity: 0;
      }
    }
    &:not(:active):not(:focus):not(:hover):not(.view-items-offer-rejected):not(.drag-over):not(.sibling-on-hover) { 
      .color-bg-light-blue {
        box-shadow: -1px 0 0 #f4f9ff, 1px 0 0 #f4f9ff;
      }
    }
  }

  // ── your sticky left column stays sticky and becomes the
  //     first grid cell automatically ─────────────────────────────────
  .view-labels {
    position: sticky;
    left: 0;
    top: 0;
    z-index: 999;
    background: #fff;
    // section headers still blue…
    .section-header {
      background: $color-primary;
      color: #fff;
    }
  }
}
.section-block__content {
  max-height: 0;
  transition: max-height 0.35s ease;
}


// hide the native scrollbar on the table container
.view-content-wrapper {
  width: 100%;
  position: relative; /* Ensures proper stacking context for Chrome */
 //transform: translateZ(0); /* Force GPU acceleration for smoother scrolling */
  //will-change: transform; /* Helps with performance in Chrome */
}

.views-field-field_offer_inquiry_counter {
  position: relative;
  /* Remove spinners in Chrome, Safari, Edge */
  input[type=text] {
    @include inputpseudonumber;
    font-size: 0.8rem;
    height: 30px;
  }
  .move-to-prev {
    @include new-arrow-left;
    visibility: hidden;
  }
  .move-to-next {
    @include new-arrow-right;
    visibility: hidden;
  }

  .move-to-prev, .move-to-next {
    position: absolute;
    bottom: 30px;
  }
  .move-to-next {
    right: map-get($spacers, 1);
  }
  .move-to-prev {
    left: map-get($spacers, 1);
  }
}
.npx-share-footer {
  & > div:first-of-type {
    border-bottom: $new-border-separator;
  }
}
.view-inquiry-offers-list {
  h4 {
    @include h4New;
  }
}

body.page-npx-offer-npx-offer-list-controller-get-offer-list .block-page-title-block {
  display: none;
}
.views-field-npx_offer_actions_views_field {
  .npx-offer-accepted button {
    @include submit-button-new;
    @include new-button-green;
    opacity: 1;
    padding: 0.5rem;
    border-radius: $new-form-border-radius;
    width: 100%;
    @include t1b;
    font-size: 1rem;
  }
}
.js .views-field-npx_offer_actions_views_field {
  .dropbutton-widget {
    @include submit-button-new;
    @include submit-button-primary;
    border-radius: $new-form-border-radius;
    border: 1px solid $primary;
    &:hover {
      border: 1px solid $color-primary;
    }
    .dropdown-item-text, .dropbutton-toggle button {
      color: #fff;
    }
    a {
      @include t1b;
      font-size: 1rem;
      &:hover {
        text-decoration: none;
      }
    }
  }
  .npx-offer-rejected button {
    @include new-button-white-bg-red-text;
    padding: 0.5rem;
    &:disabled {
      opacity: 1;
    }
  }
  .dropbutton-widget .dropbutton {
    overflow: visible;
  }
  .npx-offer-contacted .dropbutton-widget {
    @include new-button-light-blue;
    .dropdown-item-text, .dropbutton-toggle button {
      color: $link-hover-color;
    }
  }
}
.section-block--informacje-podstawowe .section-block__content{
  overflow: visible;
  background-color: #fff;
}
.npx-offer-return-label {
  @include submit-button-new;
  opacity: 1;
  padding: 0.75rem;
  border-radius: $new-form-border-radius;
  width: 100%;
  @include t1b;
  font-size: 1rem;
  @include new-button-white-bg-red-text;
  &:focus, &:active, &:hover {
    box-shadow: none;
  }
}
.views-field-contact-data.t2 {
  color: $link-hover-color;
  a {
    font-weight: 500;
    color: $link-hover-color;
  }
}
.views-field-contact-data {
//  margin-top: -15px;
//  padding-bottom: 0;
}
.views-field-trainer {
  display: flow-root;
  iframe {
    border-radius: $video-border-radius;
  }
  & + .views-field-trainer {
    margin-top: 0;
  }
}

a.npx-offer-return-link {
  text-decoration: underline;
  &:hover {
    text-decoration: none;
  }
}
.views-field-field_offer_message_1 p:last-of-type {
  margin-bottom: 0;
}

.view-inquiry-offers-list-right-overlay, .view-inquiry-offers-list-left-overlay {
  background-color: #fff;
  z-index: 9999;
  position: fixed;
}
.npx-company-title-logo .company-name-for-filters.company-name-when-logo {
  display: none;
}
body.npx-sticky-sections-active {
  .section-block--informacje-podstawowe {
    .views-field-field_offer_price_netto {
      border-bottom: $new-border-color 1px solid;
    }
  }
  .views-field-npx_offer_actions_views_field {
    display: none;
  }
  .npx-company-title-logo {
    .company-name-for-filters.company-name-when-logo {
      display: block;
    }
    img {
      display: none;
    } 
  }
  .field-business-partner, .field-trusted-partner {
    display: none;
  }
  .views-field-contact-data {
    display: none;
  }
}