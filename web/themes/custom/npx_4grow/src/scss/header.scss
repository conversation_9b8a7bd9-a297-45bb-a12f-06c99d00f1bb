@import 'common/base';
@import 'components/categorieslist';
body.user-logged-in {
  #block-userprofileblock .user-bar .menu a {
    text-transform: none;
    font-weight: 400;
  }
}

.block-sitebranding {
  min-width: 240px;
}

#header .sticky-wrapper {
  top: 0;
  width: 100%;
  background: #fff;
  z-index: 10;
  position: relative;
  &.is-sticky #sticky-header-top {
    @include shadow-bottom;
  }
}
.header {
  .js input.form-autocomplete {
    background: none;
  }
  &__landing, &__menu {
    a:not(.btn) {
      color: $body-color;
      text-decoration: none;
      &:hover {
        color: $primary
      }
    }
  }
  &__branding {
    @include media-breakpoint-down(lg) {
      .header__menu {
        position: relative;
        max-height: 90px;
      }
      #block-npx-4grow-branding {
        flex: 1 0 calc( 100% - 80px );
      }
      height: 90px;
      a {
        height: 90px;
        text-align: center;
      }
    }
    display: flex;
    flex: 1;
  }
  &__top {
    background-color: #fff;
    @include media-breakpoint-up(xxl) {
      padding: 0 2.5rem!important;
    }
    &,
    .branding-header,
    .branding-header a {
      @include media-breakpoint-up(lg) {
        height: 94px;
      }
    }
    .branding-header {
      a {
        display: block;
        padding: 11px 0;
        img {
          max-height: 100%;
          width: auto;
        }
      }
    }
  }
  &__landing {
    @include media-breakpoint-down(lg) {
      display: none!important;
    }
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    font-weight: bold;
    &-label {
      margin-right: map-get($spacers, 3);
    }
    &-items {
      a {
        display: flex;
        align-items: center;
        font-weight: 300;
        &:not(:last-of-type) {
          &:after {
            content: "";
            display: inline-block;
            width: 1px;
            height: 30px;
            margin-right: map-get($spacers, 3);
            margin-left: map-get($spacers, 3);
            background-color: #f2f2f2;
          }
        }
      }
    }
  }
  &__menu {
    @include media-breakpoint-down(md) {
      position: fixed;
      bottom:0;
      left:0;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 100%;
      z-index: 100;
    }
    & > div {
      @include media-breakpoint-up(md) {
        & {
          display: flex;
          align-items: flex-end;
          &:before {
            content: "";
            display: inline-block;
            width: 1px;
            height: 60px;
            margin-left: 10px;
            margin-right: 14px;
            background-color: #f2f2f2;
          }
        }
      }
      @include media-breakpoint-up(xl) {
        &:before {
          margin-left: map-get($spacers, 3);
          margin-right: map-get($spacers, 4);
        }
      }
    }
    .block-npx_favorite_user_block,
    .block-commerce_cart,
    #block-userprofileblock,
    .block-compare // @TODO po dodaniu modułu podmienić id
    {
      align-self: flex-end;
      margin: 2px 7px 11px 7px;
      @include media-breakpoint-up(lg) {
        margin-top: 18px;
      }
    }

    .menu-item {
      position: relative;
      &--profile {
        cursor: pointer;
        i[class^="flaticon-"] {
          font-size: 35px!important;
          line-height: 0!important;
          color: $primary!important;
        }
        &:hover {
          i[class^="flaticon-"] {
            color: inherit;
          }
          & > a {
            color: $primary!important;
          }
          .menu-item__label {
            color: $primary;
          }
        }
      }
      &__count {
        position: absolute;
        display: flex;
        background-color: $secondary;
        width: 18px;
        height: 18px;
        top: -8px;
        right: -8px;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 0.7rem;
        color: white;
      }
      &:hover {
        i[class^="flaticon-"] {
          color: $secondary;
        }
      }
      i[class^="flaticon-"] {
        position: relative;
        font-size: 20px;
        color: var(--bs-gray);
      }
      &__link, &__inner {
        display: block;
        text-align: center;
        font-weight: bold;
        text-decoration: none;
      }
      &__link:not(.btn):hover {
        color: #f27f42;
      }
      &__label {
        font-size: 0.80rem;
        font-weight: 600;
        &:hover {
          color: #f27f42;
        }
      }
    }
  }
  .header-search-region {
    background-color: var(--bs-blue-dark);
    padding: 1.5rem;
  }
}

.sticky-header-wrapper {
  background: #fff;
}

#block-userprofileblock {
  position: relative;
  cursor: default;
  .user-bar {
    .menu {
      @include media-breakpoint-down(lg) {
        text-align: left;
        font-weight: inherit;
        position: fixed;
        width: 100%;
        left:0;
        top: 90px;
        overflow-y: auto;
      }
      @include media-breakpoint-down(md) {
        height: calc(100vh - 164px);
      }
      @include media-breakpoint-up(md) {
        height: calc(100vh - 90px);
      }
      @include media-breakpoint-up(lg) {
        position: absolute;
        right: -19px;
        top: 72px;
        width: 362px;
        height: auto;
      }
      background: #fff;
      z-index: 10;
      border: 1px solid #f2f2f2;
      a {
        width: 80%;
        display: block;
        height: 100%;
        padding-bottom: 10px;
        padding-top: 10px;
        text-transform: uppercase;
        font-weight: 700;
        margin: map-get($spacers, 0) auto;
        &.btn-third {
          border: solid 1px #1072ea;
	  margin: 10px auto;
        }
      }
      p {
        font-weight: 400;
        margin-top: 10px
      }
      h3 {
        font-size: 20px;
        color: $body-color;
      }
      ul {
        list-style: none;
        padding: map-get($spacers, 0);
        margin: map-get($spacers, 0);
        & > li {
          margin: map-get($spacers, 0) 10px;
          & > ul {
            padding: 20px;
            & > li {
              margin: map-get($spacers, 0);
              padding: map-get($spacers, 0);
              border: none;
              &:last-child {
                a {
                  padding-bottom: map-get($spacers, 0);
                }
              }
            }
          }
        }
      }
      .user {
        &__profile {
          align-items: center;
        }
        &__initials {
          flex: 0 1 auto;
          width: 80px;
          height: auto;
          border-radius: 50%;
          background-color: $secondary;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          color: $white;
          font-size: 30px;
          text-transform: uppercase;
          margin-right: 10px;
          &:before {
            content:'';
            float:left;
            padding-top:100%;
          }
        }
        &__details {
          flex: 1 1 max-content;
        }
        &__name {
          font-size: 20px;
        }
        &__email {
          font-weight: 300;
        }
      }
    }
  }
}

#block-dlabiznesuheader, #block-gieldazapytanheader {
  width: 100%;
  margin-right: map-get($spacers, 2);

  @include media-breakpoint-down(lg) {
    display: none;
    margin-right: map-get($spacers, 0);
  }
  .body {
    display: flex;
    justify-content: right;
    align-items: center;
    @include media-breakpoint-down(xxl) {
      a.btn-primary.form-control {
        font-size: 13px;
      }
    }
    width: calc( 100% - 5px );
    @include media-breakpoint-up(xxl) {
      width: calc(100% - 50px);
    }
    height: 100%;
    a {
      text-wrap: nowrap;
      height: auto;
      padding: map-get($spacers, 3) .3rem;
      &:hover {
        text-decoration: none;
      }
      @include media-breakpoint-up(xl) {
        padding: map-get($spacers, 3) .75rem;
      }
    }
  }
}

body.adminimal-admin-toolbar {
  @include media-breakpoint-down(sm) {
    .branding-header .header__menu > div::before {
      left: -10px;
      top: -2px;
    }
  }
}
#block-gieldazapytanheader {
  a {
    color: #232323;
  }
}
