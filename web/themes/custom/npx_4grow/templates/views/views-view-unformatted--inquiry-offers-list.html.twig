{#
  @file
  views-view-unformatted--inquiry-offers-list.html.twig
  Renders the left “table of contents” (section headers + labels)
  in matching <section-block> wrappers,
  but only if they’ve got at least one non-empty field.
#}

{%
  set sections = {
    'informacje-podstawowe': {
      label: 'INFORMACJE PODSTAWOWE',
      keys: [
        'field_offer_inquiry_counter',
        'title',
        'npx_offer_profile_company_views_field',
        'field_offer_price_netto',
        'npx_offer_actions_views_field',
      ]
    },
    'informacje-ogolne': {
      label: 'INFORMACJE OGÓLNE',
      keys: [
        'field_offer_date_end',
        'field_offer_category',
        'npx_offer_google_rank_views_field',
        'field_offer_city',
        'field_offer_region',
        'field_offer_date_range',
        'field_offer_service_type',
        'field_offer_training_type',
      ]
    },
    'szczegoly-dotyczace-ceny-netto': {
      label: 'SZCZEGÓŁY DOTYCZĄCE CENY NETTO (W ZŁ)',
      keys: [
        'field_offer_training_price',
        'field_offer_room_price',
        'field_offer_accomodation_price',
        'field_offer_catering_price',
        'field_offer_unit_price_netto',
      ]
    },
    'benefity': {
      label: 'BENEFITY',
      keys: [
        'field_benefit1_flag',
        'field_benefit2_flag',
        'field_benefit3_flag',
        'field_benefit4_flag',
      ]
    },
    'czas-trwania': {
      label: 'CZAS TRWANIA',
      keys: [
        'field_offer_days',
        'field_offer_hours',
      ]
    },
    'uczestnicy': {
      label: 'UCZESTNICY',
      keys: [
        'field_offer_max_in_group',
        'field_offer_num_of_groups',
      ]
    },
    'trenerzy': {
      label: 'TRENERZY',
      keys: [
        'field_offer_trainers_list',
      ]
    },
    'opinie-o-partnerze': {
      label: 'OPINIE O PARTNERZE',
      keys: [
        'npx_product_offer_reviews_views_field',
      ]
    },
    'wiadomosc-od-partnera': {
      label: 'WIADOMOŚĆ OD PARTNERA',
      keys: [
        'field_offer_message_1',
      ]
    },
    'dodaj-wlasny-komentarz': {
      label: 'DODAJ WŁASNY KOMENTARZ (DLA SIEBIE)',
      keys: [
        'offer_comment_edit'
      ]
    }
  }
%}
<button class="npx-nav-btn npx-nav-btn--left position-absolute d-none" aria-label="Previous">
  <svg viewBox="0 0 24 24" class="npx-nav-btn__icon">
    <path d="M14 6 L8 12 L14 18" />
  </svg>
</button>
<div class="view-content-wrapper d-flex position-relative">
  <div class="view-labels flex-column flex-shrink-0">

    {# — Loop each section — #}
    {% for slug, section in sections %}

      {# 1) Figure out if there’s anything to show in this section #}
      {% set has_label = false %}
      {# check the “normal” keys #}
      {% for key in section.keys %}
        {% if key in visible_fields %}
          {% set has_label = true %}
        {% endif %}
      {% endfor %}
      {# if it’s “benefity”, also show if there are any extra criteria terms #}
      {% if slug == 'benefity' and other_criteria_terms|length > 0 %}
        {% set has_label = true %}
      {% endif %}

      {# 2) Only render if something made has_label = true #}
      {% if has_label %}
        <div class="section-block section-block--{{ slug }}">
          <div class="section-block__header t2b"
               data-section="{{ slug }}">
            {{ section.label }}
          </div>

          <div class="section-block__content py-2">
            {% set label_index = 0 %}

            {% if slug == 'benefity' %}
              {# — four fixed flags — #}
              {% for key in section.keys %}
                {% if key in visible_fields %}
                  {% set label_index = label_index + 1 %}
                  {% for f in view.field %}
                    {% if f.field == key %}
                      <div class="view-label t2b views-field-{{ key }} section-block--{{ slug }}
                                  {% if label_index is even %}color-bg-light-blue{% endif %}">
                        {{ f.label }}
                      </div>
                    {% endif %}
                  {% endfor %}
                {% endif %}
              {% endfor %}

              {# — dynamic other_criteria_terms — #}
              {% for tid, name in other_criteria_terms %}
                {% set label_index = label_index + 1 %}
                <div class="view-label t2b views-field-other-criteria section-block--{{ slug }}
                            {% if label_index is even %}color-bg-light-blue{% endif %}">
                  {{ name }}
                </div>
              {% endfor %}

            {% elseif slug == 'trenerzy' %}
              {# Render trainer labels up to trainers_amount_max #}
              {% for i in 1..trainers_amount_max %}
                <div class="view-label t2b views-field-trainer section-block--trenerzy{% if i is even %} color-bg-light-blue{% endif %}">
                  Trener {{ i }}
                </div>
              {% endfor %}

            {% else %}
              {# — everything else — #}
              {% for key in section.keys %}
                {% if key in visible_fields %}
                  {% set label_index = label_index + 1 %}
                  {% for f in view.field %}
                    {% if f.field == key %}
                      <div class="view-label t2b views-field-{{ key }} section-block--{{ slug }}
                                  {% if label_index is even %}color-bg-light-blue{% endif %}">
                        {{ f.label }}
                    </div>
                    {% endif %}
                  {% endfor %}
                {% endif %}
              {% endfor %}
            {% endif %}
          </div>
        </div>
      {% endif %}

    {% endfor %}
  </div>

  <div class="view-scroll-wrapper overflow-hidden">
    {% for row in rows %}
      {% set is_rejected = false %}
      {% if row.content['#row']._entity.field_offer_status.value == 'rejected' %}
        {% set is_rejected = true %}
      {% endif %}
      <div{{ row.attributes.addClass('view-items').addClass('flex-column').addClass('text-center').addClass(is_rejected ? 'view-items-offer-rejected' : '') }}>
        {{ row.content }}
      </div>
    {% endfor %}
  </div>
</div>
<button class="npx-nav-btn npx-nav-btn--right position-absolute d-none"  aria-label="Next">
  <svg viewBox="0 0 24 24" class="npx-nav-btn__icon">
    <path d="M10 6 L16 12 L10 18" />
  </svg>
</button>
