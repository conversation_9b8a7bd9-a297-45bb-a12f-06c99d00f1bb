<?php

declare(strict_types=1);

namespace Drupal\npx_checkdates;

use <PERSON><PERSON>al\Core\Config\ConfigFactoryInterface;
use <PERSON><PERSON>al\Core\Config\ImmutableConfig;
use Drupal\Core\Database\Connection;
use <PERSON>upal\Core\Datetime\DrupalDateTime;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\datetime\Plugin\Field\FieldType\DateTimeItemInterface;
use Drupal\node\NodeInterface;
use Drupal\npx_notify\NpxNotifyManagerService;
use Drupal\user\UserInterface;

/**
 * Npx check dates manager service.
 */
final class NpxCheckdatesManager {
  const SERVICE_ID = 'npx_checkdates.manager';

  const QUEUE_NAME = 'npx_checkdates_notification_queue_worker';

  private readonly ImmutableConfig $config;

  /**
   * Constructs a NpxCheckdatesManager object.
   */
  public function __construct(
    private readonly EntityTypeManagerInterface $entityTypeManager,
    private readonly MailManagerInterface $mailManager,
    private readonly ConfigFactoryInterface $configFactory,
    private readonly Connection $database,
    private readonly NpxCheckdatesStorage $npxCheckdatesStorage,
    private readonly NpxNotifyManagerService $npxNotifyManager
  ) {
    $this->config = $configFactory->get('npx_checkdates.settings');
  }

  /**
   * Prepare notification queue.
   */
  public function prepareNotificationQueue(): void {
    $uids = $this->getUsersToNotify();

    $queue = \Drupal::queue(self::QUEUE_NAME);
    foreach ($uids as $uid) {
      $data = ['uid' => (int)$uid];
      $queue->createItem($data);
    }

  }

  /**
   * Process queue item
   *
   * @param int $uid
   */
  public function processQueueItem(int $uid): void {
    $user = $this->loadUser($uid);

    if(!$user instanceof UserInterface) {
      error_log('WRONG UID: ' . $uid, 0);
      return;
    }

    if($this->npxNotifyManager->userHasDisabledNotifications($user)) {
      return;
    }

    $to = $user->getEmail();

    if($to == null) {
      return;
    }

    $subject = $this->config->get('subject');
    $message = $this->config->get('message.value');
    $message = \Drupal::token()->replace($message, ['user' => $user]);

    $this->sendEmail($subject, $message, $to);
    $this->npxCheckdatesStorage->saveItem($uid);
  }

  /**
   * Check if training has actual dates.
   *
   * @param NodeInterface $node
   * @return ActualDatesStatus
   */
  public function trainingHasActualDates(NodeInterface $node): ActualDatesStatus {
    $openDatesCount = $this->getOpenDatesCount((int)$node->id());

    if($openDatesCount == 0) {
      return ActualDatesStatus::NOT_APPLICABLE;
    }

    $openDatesCountInRange = $this->getOpenDatesCountInRange((int)$node->id());

    return $openDatesCountInRange > 0 ? ActualDatesStatus::OK : ActualDatesStatus::EXPIRED;
  }

  /**
   * Get training titles without actual dates.
   *
   * @param int $uid
   * @return array
   */
  public function getExpiredTrainingsByUid(int $uid): array {
    $now = new DrupalDateTime();
    $now->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);

    $query = $this->database->select('commerce_product_field_data', 'cp');
    $query->leftJoin('commerce_product__npx_event_date', 'ed', 'cp.product_id=ed.entity_id');
    $query->condition('cp.type' , 'default')
      ->condition('cp.uid', $uid)
      ->condition('ed.npx_event_date_value' , $now, '<=')
      ->condition('ed.deleted' , 0)
      ->fields('cp', ['npx_training']);

    $titles = [];
    $nids = $this->flattenResultsArray($query->distinct()->execute()->fetchAll());
    $nodes = $this->entityTypeManager->getStorage('node')->loadMultiple(array_values($nids));

    /** @var NodeInterface $node */
    foreach($nodes as $node) {
      if(!$this->getOpenDatesCountInRange((int)$node->id())) {
        $titles[] = $node->getTitle();
      }
    }

    return $titles;
  }

  private function getOpenDatesCount(int $nid): int {
    $query = $this->entityTypeManager->getStorage('commerce_product')->getQuery()->accessCheck(false);
    $query->condition('type', 'default')
      ->condition('npx_training', $nid);
    return $query->count()->execute();
  }

  private function getOpenDatesCountInRange(int $nid): int {
    $now = new DrupalDateTime();
    $now->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);
//     $future = new DrupalDateTime('+2 months');
//     $future = $future->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);

    $query = $this->entityTypeManager->getStorage('commerce_product')->getQuery()->accessCheck(false);
    $query->condition('type', 'default')
      ->condition('npx_training', $nid)
      ->condition('npx_event_date.value', $now, '>=');
//       ->condition('npx_event_date.value', $future, '<=');
    return $query->count()->execute();
  }

  private function getUsersToNotify(): array {
    $all_uids = $this->getOpenProductsOwners();
    $uids_with_dates = $this->getOpenProductsOwnersInRange();
    $sent = new DrupalDateTime('-1 month');
    $uids_sent = $this->flattenResultsArray($this->npxCheckdatesStorage->getItemsSentAfter($sent->getTimestamp()));

    $uids = array_diff($all_uids, $uids_with_dates);
    $uids = array_diff($uids, $uids_sent);

    return $uids;
  }

  private function getOpenProductsOwners(): array {
    $query = $this->database->select('commerce_product_field_data', 'cp')
      ->condition('cp.type' , 'default')
      ->fields('cp', ['uid']);

    return $this->flattenResultsArray($query->distinct()->execute()->fetchAll());
  }

  private function getOpenProductsOwnersInRange(): array {
    $now = new DrupalDateTime();
    $now->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);
//     $future = new DrupalDateTime('+2 months');
//     $future = $future->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);

    $query = $this->database->select('commerce_product_field_data', 'cp');
    $query->leftJoin('commerce_product__npx_event_date', 'ed', 'cp.product_id=ed.entity_id');
    $query->condition('cp.type' , 'default')
      ->condition('ed.npx_event_date_value' , $now, '>=')
//       ->condition('ed.npx_event_date_value' , $future, '<=')
      ->condition('ed.deleted' , 0)
      ->fields('cp', ['uid']);

    $result = $query->distinct()->execute()->fetchAll();

    return $this->flattenResultsArray($result);
  }

  private function flattenResultsArray(array $input): array {
    $output = [];
    foreach($input as $val) {
      $values = array_values(get_object_vars($val));
      $output[] = reset($values);
    }

    return $output;
  }

  private function loadUser(int $uid): ?UserInterface {
    return $this->entityTypeManager->getStorage('user')->load($uid);
  }

  private function sendEmail(string $subject, string $message, string $to): void {
    $from = $this->configFactory->get('system.site')->get('mail');
    $module = 'npx_checkdates';
    $key = 'npx_checkdates';
    $langcode = \Drupal::languageManager()->getDefaultLanguage()->getId();

    $params = [];
    $params['subject'] = $subject;
    $params['body'] = $message;
    $params['from'] = $from;

    $this->mailManager->mail($module, $key, $to, $langcode, $params, $from);
  }
}
