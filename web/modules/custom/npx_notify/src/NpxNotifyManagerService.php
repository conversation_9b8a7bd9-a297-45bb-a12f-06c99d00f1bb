<?php

declare(strict_types=1);

namespace Drupal\npx_notify;

use Dr<PERSON>al\Core\Datetime\DrupalDateTime;
use Drupal\Core\Entity\EntityStorageException;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Mail\MailManagerInterface;
use Drupal\Core\Render\RendererInterface;
use <PERSON><PERSON>al\comment\CommentInterface;
use Drupal\datetime\Plugin\Field\FieldType\DateTimeItemInterface;
use Drupal\node\NodeInterface;
use Drupal\npx_notify\Entity\NpxNotifyProfile;
use Drupal\Core\Config\ConfigFactoryInterface;
use Drupal\Core\Config\ImmutableConfig;
use Drupal\Core\Session\AccountInterface;
use Drupal\npx_offer\NpxOfferManagerService;

/**
 * Npx Notify Manager Service.
 */
final class NpxNotifyManagerService {
  const SERVICE_ID = 'npx_notify.manager';

  const PROFILE_ENTITY = 'npxnotify_profile';
  const PROFILE_CATEGORY_FIELD = 'field_training_category_new';
  const PROFILE_SUBSCRIBED_INQUIRY_FIELD = 'field_subscribed_inquiry';
  const PROFILE_SEND_ALL_COMMENTS_FIELD = 'field_send_all_comments';
  const PROFILE_DISABLE_NOTIFICATIONS_FIELD = 'field_disable_notifications';
  const INQUIRY_NODE_BUNDLE = 'npx_inquiry';
  const INQUIRY_CATEGORY_FIELD = 'field_offer_category_new';
  const INQUIRY_COMMENT_BUNDLE = 'inquiry_comment';
  const QUEUE_NAME = 'npx_notify_notification_queue_worker';
  const QUEUE_ITEM_TYPE_INQUIRY = 'inquiry';
  const QUEUE_ITEM_TYPE_INQUIRY_COMMENT = 'inquiry_comment';

  // EXPIRE FIELDS
  const INQUIRY_MAX_OFFER_SUBMISSION_DATE_FIELD = 'field_offer_datetime_end';
  CONST INQUIRY_MAX_OFFER_EXPIRY_DATE_FIELD = 'field_offer_date_end';

  const EXPIRED_QUEUE_NAME = 'npx_notify_expired_notification_queue_worker';

  private readonly ImmutableConfig $config;

  /**
   * Constructs a NpxNotifyManagerService object.
   */
  public function __construct(
    private readonly EntityTypeManagerInterface $entityTypeManager,
    private readonly MailManagerInterface $mailManager,
    private readonly RendererInterface $renderer,
    private readonly ConfigFactoryInterface $configFactory,
    private readonly NpxInquiryExpireStorage $npxInquiryExpireStorage,
    private readonly NpxOfferManagerService $npxOfferManager
  ) {
    $this->config = $configFactory->get('npx_notify.settings');
  }

  /**
   * Checks if user has NpxNotifyProfile entity.
   *
   * @param AccountInterface $account
   * @return bool
   */
  public function userHasNotifyProfile(AccountInterface|null $account=null): bool {
    if(!$account) {
      $account = \Drupal::currentUser();
    }
    $result = $this->loadProfilesByProperties(['uid' => $account->id()]);

    return count($result) > 0 ? true : false;
  }

  /**
   * Gets the user NpxNotifyProfile entity.
   *
   * @param AccountInterface $account
   * @return NpxNotifyProfile|NULL
   */
  public function getUserNotifyProfile(AccountInterface|null $account=null): NpxNotifyProfile|null {
    if(!$account) {
      $account = \Drupal::currentUser();
    }
    $result = $this->loadProfilesByProperties(['uid' => $account->id()]);

    $profile = reset($result);

    return ($profile instanceof NpxNotifyProfile) ? $profile : null;
  }


  /**
   * Delete user profile.
   *
   * @param AccountInterface|null $account
   * @return bool
   */
  public function deleteUserNotifyProfile(AccountInterface|null $account=null): bool {
    $profile = $this->getUserNotifyProfile($account);

    if($profile instanceof NpxNotifyProfile) {
      $profile->delete();
      return true;
    }

    return false;
  }

  public function saveCategoriesAction(array $ids): bool {
    $profile = $this->getUserNotifyProfile();
    if(!$profile instanceof NpxNotifyProfileInterface) {
      $account = \Drupal::currentUser();
      $profile = $this->entityTypeManager->getStorage(self::PROFILE_ENTITY)->create([
        'uid' => $account->id(),
        'label' => 'UID: ' . $account->id() . ' ' . $account->getAccountName(),
      ]);
    }

    $profile->set(self::PROFILE_CATEGORY_FIELD, array_values($ids));
    try {
      $profile->save();
    } catch(EntityStorageException $e) {
      return false;
    }

    return true;
  }

  public function getUserSubscribedCategories(AccountInterface|null $account = null): array {
    if(!$account) {
      $account = \Drupal::currentUser();
    }

    $storage = $this->entityTypeManager->getStorage(self::PROFILE_ENTITY);
    $query = $storage->getQuery()
      ->condition('uid', $account->id())
      ->accessCheck(FALSE);
    $ids = $query->execute();

    if (empty($ids)) {
      return [];
    }

    $profile = $storage->load(reset($ids));
    if (!$profile) {
      return [];
    }

    $result = [];
    $values = $profile->get(self::PROFILE_CATEGORY_FIELD)->getValue();
    foreach ($values as $val) {
      $result[] = $val['target_id'];
    }

    return $result;
  }

  public function hasSubscribeInquiryComments(int $nid): bool {
    $profile = $this->getUserNotifyProfile();
    if(!$profile instanceof NpxNotifyProfileInterface) {
      return false;
    }

    $entities = $profile->get(self::PROFILE_SUBSCRIBED_INQUIRY_FIELD)->referencedEntities();

    foreach($entities as $entity) {
      if($entity->id() == $nid) {
        return true;
      }
    }

    return false;
  }

  public function addSubscribeInquiryCommentsAction(int $nid) {
    $profile = $this->getUserNotifyProfile();
    if(!$profile instanceof NpxNotifyProfileInterface) {
      $profile = $this->entityTypeManager->getStorage(self::PROFILE_ENTITY)->create();
    }

    $ids = [];

    $entities = $profile->get(self::PROFILE_SUBSCRIBED_INQUIRY_FIELD)->referencedEntities();

    foreach($entities as $entity) {
      $ids[] = $entity->id();
    }

    if(!in_array($nid, $ids)) {
      $ids[] = $nid;
    }

    $profile->set(self::PROFILE_SUBSCRIBED_INQUIRY_FIELD, $ids);
    try {
      $profile->save();
    } catch(EntityStorageException $e) {
      return false;
    }

    return true;
  }

  public function removeSubscribeInquiryCommentsAction(int $nid) {
    $profile = $this->getUserNotifyProfile();
    if(!$profile instanceof NpxNotifyProfileInterface) {
      $profile = $this->entityTypeManager->getStorage(self::PROFILE_ENTITY)->create();
    }

    $ids = [];

    $entities = $profile->get(self::PROFILE_SUBSCRIBED_INQUIRY_FIELD)->referencedEntities();

    foreach($entities as $entity) {
      if($entity->id() != $nid) {
        $ids[] = $entity->id();
      }
    }

    $profile->set(self::PROFILE_SUBSCRIBED_INQUIRY_FIELD, $ids);
    try {
      $profile->save();
    } catch(EntityStorageException $e) {
      return false;
    }

    return true;
  }

  /**
   * Add items to notification queue.
   *
   * @param NodeInterface $node
   */
  public function prepareNotificationQueue(NodeInterface $node): void {
    $categories = $this->getCategories($node);

    $query = \Drupal::entityQuery(self::PROFILE_ENTITY)
    ->condition(self::PROFILE_CATEGORY_FIELD, $categories, 'IN')
    ->condition(self::PROFILE_DISABLE_NOTIFICATIONS_FIELD, false)
    ->accessCheck(FALSE);

    $ids = $query->execute();

    $queue = \Drupal::queue(self::QUEUE_NAME);
    foreach ($ids as $pid) {
      $data = [
        'nid' => (int)$node->id(),
        'pid' => (int)$pid,
        'type' => self::QUEUE_ITEM_TYPE_INQUIRY,
      ];
      $queue->createItem($data);
    }
  }

  /**
   * Add items to notification queue.
   *
   * @param CommentInterface $comment
   */
  public function prepareCommentNotificationQueue(CommentInterface $comment): void {
    /** @var \Drupal\node\NodeInterface $node */
    $node = $comment->getCommentedEntity();
    $categories = $this->getCategories($node);

    $query = \Drupal::entityQuery(self::PROFILE_ENTITY);
    $group_category = $query->andConditionGroup()
      ->condition(self::PROFILE_CATEGORY_FIELD, $categories, 'IN')
      ->condition(self::PROFILE_DISABLE_NOTIFICATIONS_FIELD, false)
      ->condition(self::PROFILE_SEND_ALL_COMMENTS_FIELD, true);

    $group_main = $query->orConditionGroup()
      ->condition($group_category)
      ->condition(self::PROFILE_SUBSCRIBED_INQUIRY_FIELD, $node->id(), 'IN');

    $query->condition($group_main);
    $query->accessCheck(FALSE);

    $ids = $query->execute();

    $queue = \Drupal::queue(self::QUEUE_NAME);
    foreach ($ids as $pid) {
      $data = [
        'nid' => (int)$node->id(),
        'pid' => (int)$pid,
        'type' => self::QUEUE_ITEM_TYPE_INQUIRY_COMMENT,
      ];
      $queue->createItem($data);
    }
  }

  /**
   * Sends notification to notify profile owner.
   *
   * @param int $nid
   * @param int $pid
   */
  public function processQueryItem(int $nid, int $pid, string $type): void {
    $node = $this->loadNode($nid);
    $profile = $this->loadProfile($pid);
    $to = $profile->getOwner()->getEmail();

    if($this->userHasDisabledNotifications($profile->getOwner())) {
      return;
    }

    switch($type) {
      case self::QUEUE_ITEM_TYPE_INQUIRY:
        $subject = $this->config->get('subject');
        $message = $this->config->get('message.value');
        $message = \Drupal::token()->replace($message, ['node' => $node]);
        break;
      case self::QUEUE_ITEM_TYPE_INQUIRY_COMMENT:
        $subject = $this->config->get('subject_comment');
        $message = $this->config->get('message_comment.value');
        $message = \Drupal::token()->replace($message, ['node' => $node]);
        break;
    }

    $this->sendEmail($subject, $message, $to);
  }

  /**
   * Add items to expired notification queue.
   */
  public function prepareExpiredQueue(): void {
    $submissionExpIds = $this->getSubmissionExpiredIds();
    $offerExpIds = $this->getOffersExpiredIds();

    $queue = \Drupal::queue(self::EXPIRED_QUEUE_NAME);

    foreach ($submissionExpIds as $nid) {
      $data = [
        'nid' => (int)$nid,
        'type' => InquiryExpireDateType::SUBMISSION_EXPIRED,
      ];
      $queue->createItem($data);
    }

    foreach ($offerExpIds as $nid) {
      $data = [
        'nid' => (int)$nid,
        'type' => InquiryExpireDateType::OFFERS_EXPIRED,
      ];
      $queue->createItem($data);
    }
  }

  /**
   * Sends notification to inquiry owner.
   *
   * @param int $nid
   * @param InquiryExpireDateType $type
   */
  public function processExpiredQueryItem(int $nid, InquiryExpireDateType $type): void {
    if($this->npxInquiryExpireStorage->itemExists($nid, $type)) {
      return;
    }

    $node = $this->loadNode($nid);

    if($this->userHasDisabledNotifications($node->getOwner())) {
      return;
    }

    $to = $node->getOwner()->getEmail();

    switch($type) {
      case InquiryExpireDateType::OFFERS_EXPIRED:
        $subject = $this->config->get('subject_offers_expired');
        $message = $this->config->get('message_offers_expired.value');
        $message = \Drupal::token()->replace($message, ['node' => $node]);

        if($this->npxOfferManager->inquiryHasPendingOffers($nid)) {
          $this->sendEmail($subject, $message, $to);
        }
        break;
      case InquiryExpireDateType::SUBMISSION_EXPIRED:
        $subject = $this->config->get('subject_submission_expired');
        $message = $this->config->get('message_submission_expired.value');
        $message = \Drupal::token()->replace($message, ['node' => $node]);

        $this->sendEmail($subject, $message, $to);
        break;
    }

    $this->npxInquiryExpireStorage->addItem($nid, $type);
  }

  /**
   * Send new comment admin email notification
   * @param CommentInterface $comment
   */
  public function sendAdminNewCommentNotification(CommentInterface $comment): void {
    $node = $comment->getCommentedEntity();
    $enabled = $this->config->get('enable_admin');

    if($enabled) {
      $to = $this->config->get('enable_admin');
      $subject = $this->config->get('subject_admin');
      $message = $this->config->get('message_admin.value');
      $message = \Drupal::token()->replace($message, ['node' => $node]);

      $this->sendEmail($subject, $message, $to);
    }
  }

  /**
   * Checks if user has disabled notifications.
   *
   * @param AccountInterface|null $account
   * @return bool
   */
  public function userHasDisabledNotifications(AccountInterface|null $account=null): bool {
    $profile = $this->getUserNotifyProfile($account);
    if($profile) {
      $disable = $profile->get(self::PROFILE_DISABLE_NOTIFICATIONS_FIELD, false)->value;
      return $disable != false ? true : false;
    }
    return false;
  }

  private function getSubmissionExpiredIds(): array {
    $now = new DrupalDateTime();
    $now->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);
    $weekAgo = new DrupalDateTime('-7 days');
    $weekAgo = $weekAgo->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);

    $query = $this->entityTypeManager->getStorage('node')->getQuery()->accessCheck(false)
    ->condition('type', self::INQUIRY_NODE_BUNDLE)
    ->condition(self::INQUIRY_MAX_OFFER_SUBMISSION_DATE_FIELD, $now, '<=')
    ->condition(self::INQUIRY_MAX_OFFER_SUBMISSION_DATE_FIELD, $weekAgo, '>=');
    $ids = $query->execute();

    return $ids;
  }

  private function getOffersExpiredIds(): array {
    $now = new DrupalDateTime();
    $now->format(DateTimeItemInterface::DATE_STORAGE_FORMAT);
    $weekAgo = new DrupalDateTime('-7 days');
    $weekAgo = $weekAgo->format(DateTimeItemInterface::DATE_STORAGE_FORMAT);

    $query = $this->entityTypeManager->getStorage('node')->getQuery()->accessCheck(false)
    ->condition('type', self::INQUIRY_NODE_BUNDLE)
    ->condition(self::INQUIRY_MAX_OFFER_EXPIRY_DATE_FIELD, $now, '<=')
    ->condition(self::INQUIRY_MAX_OFFER_EXPIRY_DATE_FIELD, $weekAgo, '>=');
    $ids = $query->execute();

    return $ids;
  }

  private function getCategories(NodeInterface $node): array {
    $result = [];
    $values = $node->get(self::INQUIRY_CATEGORY_FIELD)->getValue();

    foreach($values as $val) {
      $result[] = $val['target_id'];
    }

    return $result;
  }

  private function loadProfile(int $pid): ?NpxNotifyProfile {
    return $this->entityTypeManager->getStorage(self::PROFILE_ENTITY)->load($pid);
  }

  private function loadProfiles(array $pids): array {
    return $this->entityTypeManager->getStorage(self::PROFILE_ENTITY)->loadMultiple($pids);
  }

  private function loadProfilesByProperties(array $properties): array {
    return $this->entityTypeManager->getStorage(self::PROFILE_ENTITY)->loadByProperties($properties);
  }

  private function loadNode(int $nid): ?NodeInterface {
    return $this->entityTypeManager->getStorage('node')->load($nid);
  }

  private function sendEmail(string $subject, string $message, string $to): void {
    $from = $this->configFactory->get('system.site')->get('mail');
    $module = 'npx_notify';
    $key = 'npx_notify';
    $langcode = \Drupal::languageManager()->getDefaultLanguage()->getId();

    $params = [];
    $params['subject'] = $subject;
    $params['body'] = $message;
    $params['from'] = $from;

    $this->mailManager->mail($module, $key, $to, $langcode, $params, $from);
  }
}
