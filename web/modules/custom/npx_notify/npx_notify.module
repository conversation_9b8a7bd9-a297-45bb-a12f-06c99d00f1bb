<?php

/**
 * @file
 * Primary module hooks for Npx notify module.
 */

use Dr<PERSON>al\Core\Access\AccessResult;
use <PERSON><PERSON>al\Core\Ajax\AjaxResponse;
use <PERSON><PERSON>al\Core\Ajax\OpenModalDialogCommand;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Queue\SuspendQueueException;
use Drupal\Core\Session\AccountInterface;
use Dr<PERSON>al\comment\CommentInterface;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use Drupal\npx_notify\NpxNotifyManagerService;

/**
 * Implements hook_entity_create_access().
 *
 * @see: hook_entity_access().
 */
function npx_notify_entity_create_access(AccountInterface $account, array $context, ?string $entity_bundle) {
  $result = false;
  if ($context['entity_type_id'] == 'npxnotify_profile') {
    /** @var \Drupal\npx_notify\NpxNotifyManagerService $manager */
    $manager = \Drupal::service('npx_notify.manager');
    $result = $manager->userHasNotifyProfile();
  }

  return ($result) ? AccessResult::forbidden() : AccessResult::neutral();
}

/**
 * Implements hook_ENTITY_TYPE_insert().
 *
 * @see: hook_entity_insert().
 */
function npx_notify_node_insert(NodeInterface $node) {
  if($node->getType() == 'npx_inquiry') {
    /** @var \Drupal\npx_notify\NpxNotifyManagerService $manager */
    $manager = \Drupal::service(NpxNotifyManagerService::SERVICE_ID);
    $manager->prepareNotificationQueue($node);
  }
}

/**
 * Implements hook_ENTITY_TYPE_insert().
 */
function npx_notify_comment_insert(CommentInterface $comment) {
  if($comment->bundle() == NpxNotifyManagerService::INQUIRY_COMMENT_BUNDLE) {
    /** @var \Drupal\npx_notify\NpxNotifyManagerService $manager */
    $manager = \Drupal::service(NpxNotifyManagerService::SERVICE_ID);
    $manager->prepareCommentNotificationQueue($comment);
    $manager->sendAdminNewCommentNotification($comment);
  }
}

/**
 * Implements hook_mail().
 */
function npx_notify_mail($key, &$message, $params) {
  switch ($key) {
    case 'npx_notify':
      $message['from'] = $params['from'];
      $message['subject'] = $params['subject'];
      $message['body'][] = $params['body'];
      $message['headers']['Content-Type'] = 'text/html;';
      $message['headers']['From'] = $params['from'];
      break;
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function npx_notify_form_views_exposed_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  /** @var \Drupal\views\ViewExecutable $view */
  $view = $form_state->get('view');
  $view_id = $view->id();
  $display = $view->current_display;

  $account = \Drupal::currentUser();

  if($account->hasPermission('create npxnotify_profile') && $view_id == 'inquiry_stock_marget') {
    $form['actions']['submit']['#suffix'] = '<a href="#" id="npx-notify-save-action-btn" class="button form-submit form-control text-center">' . t('Save npxnotify') . '</a>';
    $form['#attached']['library'][] = 'npx_notify/save_action';
//     $form['#cache']['max-age'] = 0;
  }
}

/**
 * Implements hook_form_alter().
 */
function npx_notify_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  if ($form_id == 'npxnotify_profile_edit_form' || $form_id == 'npxnotify_profile_add_form') {
    $form['#title'] = t('Zarządzanie powiadomieniami o zapytaniach ofertowych');
  }
}

/**
 * Implements hook_theme().
 */
function npx_notify_theme() {
  return [
    'field_group_html_element__npxnotify_profile__group_npx_notify_profile_intro' => [
      'render element' => 'elements',
      'template' => 'field-group-html-element--npxnotify-profile--group-npx-notify-profile-intro',
      'path' => \Drupal::service('extension.list.module')->getPath('npx_notify') . '/templates',
    ],
  ];
}

/**
 * Implements hook_cron().
 */
function npx_notify_cron() {
  /** @var \Drupal\npx_notify\NpxNotifyManagerService $manager */
  $manager = \Drupal::service(NpxNotifyManagerService::SERVICE_ID);
  $manager->prepareExpiredQueue();
}

/**
 * Implements hook_views_pre_render().
 */
function npx_notify_views_pre_render(\Drupal\views\ViewExecutable $view) {
  if ($view->id() === 'inquiry_stock_marget') {
    $now = new \DateTime();

    foreach ($view->result as $row) {
      if (isset($row->_entity) && $row->_entity->hasField('field_offer_datetime_end')) {
        $end_date = $row->_entity->get('field_offer_datetime_end')->value;

        if ($end_date) {
          $end_datetime = new \DateTime($end_date);
          $is_expired = $end_datetime < $now;

          // Dodaj informację o wygaśnięciu do row
          $row->is_expired = $is_expired;
        }
      }
    }
  }
}

/**
 * Implements hook_preprocess_views_view_field().
 */
function npx_notify_preprocess_views_view_field(&$variables) {
  $view = $variables['view'];
  $field = $variables['field'];

  // Sprawdź czy to odpowiedni view i field
  if ($view->id() == 'inquiry_stock_marget' && $field->field == 'title') {
    $current_user = \Drupal::currentUser();
    $row = $variables['row'];

    if (isset($row->_entity)) {
      $node = $row->_entity;

      // Sprawdź rolę HR i autorstwo - jeśli to nie jego inquiry, usuń link
      if ($current_user->hasRole('hr') && $node->getOwnerId() != $current_user->id()) {
        // Usuń link i zostaw tylko tytuł
        $variables['output'] = [
          '#type' => 'html_tag',
          '#tag' => 'span',
          '#value' => $node->getTitle(),
        ];
      }
    }
  }
}

/**
 * implements hook_ENTITY_TYPE_predelete().
 */
function npx_notify_user_predelete(AccountInterface $account) {
  /** @var \Drupal\npx_notify\NpxNotifyManagerService $manager */
  $manager = \Drupal::service(NpxNotifyManagerService::SERVICE_ID);
  $manager->deleteUserNotifyProfile($account);
}

