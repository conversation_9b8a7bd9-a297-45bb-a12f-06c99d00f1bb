<?php

namespace Drupal\npx_search;

use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\mysql\Driver\Database\mysql\Connection;
use Drupal\npx_main\NpxVocabularies;
use Drupal\Core\Routing\CurrentRouteMatch;
use Symfony\Component\HttpFoundation\RequestStack;
use Drupal\Component\Utility\UrlHelper;
use Drupal\taxonomy\TermInterface;
use Drupal\npx_main\NpxTrainingFields;
use Drupal\node\NodeInterface;
use Drupal\npx_training\NpxTrainingManagerServiceInterface;
use Drupal\commerce_product\Entity\Product;
use Drupal\npx_main\NpxDateUtils;
use Drupal\npx_training\TrainingDateFields;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Datetime\DrupalDateTime;
use Drupal\commerce_price\Price;
use Drupal\npx_search_cache\NpxSearchCacheManagerInterface;
use Drupal\npx_search_cache\Entity\NpxSearchCacheEntityInterface;
use Drupal\npx_search_cache\NpxSearchCacheQuery;
use Drupal\npx_discount\NpxCalculatorInterface;
use Drupal\address\Plugin\Field\FieldType\AddressItem;
use Drupal\Core\Datetime\DateFormatterInterface;
use Drupal\datetime\Plugin\Field\FieldType\DateTimeItemInterface;
use Drupal\npx_search_cache\NpxSearchCacheManagerService;

/**
 * Class NpxSearchManagerService.
 */
class NpxSearchManagerService implements NpxSearchManagerInterface {

  /**
   * Drupal\Core\Entity\EntityTypeManagerInterface definition.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * Drupal\Core\Routing\CurrentRouteMatch definition.
   *
   * @var \Drupal\Core\Routing\CurrentRouteMatch
   */
  protected $currentRouteMatch;

  /**
   * @var \Symfony\Component\HttpFoundation\RequestStack
   */
  protected $request;

  /**
   * Drupal\mysql\Driver\Database\mysql\Connection definition.
   *
   * @var \Drupal\mysql\Driver\Database\mysql\Connection
   */
  protected $database;

  /**
   * Drupal\Core\Datetime\DateFormatterInterface definition.
   *
   * @var \Drupal\Core\Datetime\DateFormatterInterface
   */
  protected $dateFormatter;

  /**
   * Drupal\npx_training\NpxTrainingManagerServiceInterface definition.
   *
   * @var \Drupal\npx_training\NpxTrainingManagerServiceInterface
   */
  protected $npxTrainingManager;

  /*
   * Drupal\npx_search_cache\NpxSearchCacheManagerInterface definition.
   *
   * @var \Drupal\npx_search_cache\NpxSearchCacheManagerInterface
   */
  protected $npxSearchCacheManager;

  /**
   * Drupal\npx_discount\NpxCalculatorInterface definition.
   *
   * @var \Drupal\npx_discount\NpxCalculatorInterface
   */
  protected $npxDiscountCalculator;

  /**
   * Constructs a new NpxSearchManagerService object.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    CurrentRouteMatch $current_route_match,
    RequestStack $request,
    Connection $database,
    DateFormatterInterface $date_formatter,
    NpxTrainingManagerServiceInterface $npx_training_manager,
    NpxSearchCacheManagerInterface $npx_searchcache_manager,
    NpxCalculatorInterface $npx_discount_calculator
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->currentRouteMatch = $current_route_match;
    $this->request = $request;
    $this->database = $database;
    $this->dateFormatter = $date_formatter;
    $this->npxTrainingManager = $npx_training_manager;
    $this->npxDiscountCalculator = $npx_discount_calculator;
    $this->npxSearchCacheManager = $npx_searchcache_manager;
  }

  /*
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::doTextSearch()
   */

  public function doTextSearch(string $phrase): array {
    $nids = [];

    /** @var \\Drupal\search_api\Entity\Index $index */
    $index = \Drupal\search_api\Entity\Index::load('default_index');

    /** @var \Drupal\search_api\Query\Query $query */
    $query = $index->query();
//     $query->setFulltextFields(['title', 'company']);
    $query->keys($phrase);

    /** @var \Drupal\search_api\Query\ResultSetInterface $results */
    $results = $query->execute();

    $items = $results->getResultItems();
    /** @var \Drupal\search_api\Item\Item $item */
    foreach($items as $item) {
      $item_id = $this->parseResultItemUri($item->getId());
      $nids[$item_id] = $item_id;
    }

    return $nids;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getAllCities()
   */
  public function getAllCities(): array {
//     return $this->entityTypeManager->getStorage('taxonomy_term')->loadTree(NpxVocabularies::CITY, 0, null, true);
    return $this->entityTypeManager->getStorage('taxonomy_term')->loadTree(NpxVocabularies::CITY);
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getAllTrainingTypes()
   */
  public function getAllTrainingTypes(): array {
    return $this->entityTypeManager->getStorage('taxonomy_term')->loadTree(NpxVocabularies::TRAINING_TYPE);
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getTextPhraseVariable()
   */
  public function getTextPhraseVariable(): string {
    return $this->getQueryParam('text_phrase') ?? '';
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getTrainingTypeVariable()
   */
  public function getTrainingTypeVariable(): mixed {
    return $this->getQueryParam('training_type') ?? '';
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getProductTypeVariable()
   */
  public function getProductTypeVariable(): mixed {
    return $this->getQueryParam('product_type') ?? '';
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getTrainingCityVariable()
   */
  public function getTrainingCityVariable(): ?string {
    return $this->getQueryParam('training_city');
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getTrainingCityVariableTerm()
   */
  public function getTrainingCityVariableTerm(): ?TermInterface {
    $tid = (int) $this->getQueryParam('training_city');
    $term = null;

    if($tid > 0) {
      $term = $this->loadTerm($tid);
    }

    return $term;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getTrainingCategoryParam()
   */
  public function getTrainingCategoryParam(): ?TermInterface {
    $term = $this->currentRouteMatch->getParameter('term');

    return $term;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getBenefitsFilters()
   */
  public function getBenefitsFilters(): array {
    $terms = $this->loadAllTerms(NpxVocabularies::BENEFITS, true);
    $filters = [];

    /** @var \Drupal\taxonomy\TermInterface $term */
    foreach($terms as $term) {
      $filters[$term->id()] = [
          'id' => $term->id(),
          'label' => $term->getName(),
          'header' => $term->get('field_text_header')->value,
          'fields' => $this->getBenefitRelatedFieldData($term),
      ];
    }

    return $filters;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::buildTiles()
   */
  public function buildTiles(array &$nodes, string $tile_type=null): array {
    if(count($nodes) == 0) {
      return [];
    }
    if($tile_type) {
      foreach($nodes as $node) {
        $node->_npx_tile_type = $tile_type;
      }
    } else {
      foreach($nodes as $node) {
        $node->_npx_tile_type = null;
      }
    }

    $result = $this->entityTypeManager->getViewBuilder('node')->viewMultiple($nodes, 'npx_search_tile');

    return $result;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getClosestDatePrice()
   */
  public function getClosestDatePriceInfo(NodeInterface $node): array {
    $price_data = [];
//     $date_start = $this->getFilterParam('filter_date_from', null);
//     $date_end = $this->getFilterParam('filter_date_to', null);
    $quantity = (int) $this->getFilterParam('filter_quantity', 1);
    $exclude_prepaid = $this->getFilterParam('filter_prepaid_disabled', false);
    $price_type = $this->getFilterParam('filter_price_type', 'brutto');
    $training_city = $this->getFilterParam('training_city', null);

    if(!$training_city) {
      $training_city = $this->getTrainingCityVariable();
    }

    if(!($quantity > 0)) {
      $quantity = 1;
    }

//     $product = $this->npxTrainingManager->getClosestTrainingDate($node, $date_start, $date_end);
    $cache_query = $this->createSearchCacheQuery([$node->id()]);
    if ($training_city != '' && $training_city != 'Cała Polska' && $training_city != 'Cały kraj') {
      $cache_query->setCity($training_city);
    }
    $tile_type = $this->getFilterParam('tile_type', $node->_npx_tile_type);
    if($tile_type == NpxProductClosed::getId() || $tile_type == NpxProductToOrder::getId()) {
      $cache_query->setDateFrom(null);
      $cache_query->setDateTo(null);
      $cache_query->setProductType($tile_type);
      $product = $this->npxSearchCacheManager->findClosestDate($cache_query);
    } else {
      $product = $this->npxSearchCacheManager->findClosestDate($cache_query);
    }

    if(!$product) {
      $cache_query->setDateFrom(null);
      $cache_query->setDateTo(null);
      $product = $this->npxSearchCacheManager->findClosestDate($cache_query);
    }

    if(!$product && NpxSearchCacheManagerService::PROCESS_OLD_PRODUCTS) {
      $date_now = new DrupalDateTime('now');
      $date_now->modify('-1 days');
      $date_now->setTimezone(new \DateTimezone(DateTimeItemInterface::STORAGE_TIMEZONE));
      $formatted_now_minus = $date_now->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);

      $cache_query->setDateFrom('1970-01-01');
      $cache_query->setDateTo($formatted_now_minus);
      $product = $this->npxSearchCacheManager->findClosestDate($cache_query);
    }

    if($product instanceof Product) {
      $hide_price = false;
      if($product->hasField('field_hide_price') && $product->get('field_hide_price')->value == true) {
        $hide_price = true;
      }
      if($product->bundle() == NpxProductClosed::getId()) {
        $price_info = \Drupal::service('npx_discount.calculator')->getPriceInfo($product, 1);
      } else {
        $price_info = \Drupal::service('npx_discount.calculator')->getPriceInfo($product, $quantity);
      }

      $cache_item = $this->npxSearchCacheManager->getCacheItem($node->id(), $product->id(), $quantity);

      if($cache_item instanceof NpxSearchCacheEntityInterface) {
        $price_data = [
            'regular' => $price_info['regular'],
            'regular_netto' => $price_info['regular_netto'],
            'discounted' => $price_info['discounted'],
            'discounted_netto' => $price_info['discounted_netto'],
            'prepaid_discounted' => $price_info['discounted_prepaid'],
            'prepaid_discounted_netto' => $price_info['discounted_prepaid_netto'],
            'prepaid_days' => $cache_item->getPrepaidDays(),
            'quantity' => $quantity . ' ' . ($quantity > 1 ? 'uczestników' : 'uczestnika'),
            'exclude_prepaid' => $exclude_prepaid,
            'price_type' => $price_type,
            'quantity_number' => $quantity,
            'hide_price' => $hide_price,
        ];
      }
    }

    return $price_data;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getClosestDate()
   */
  public function getClosestDate(NodeInterface $node): ?Product {
    $cache_query = $this->createSearchCacheQuery([$node->id()]);
    $training_city = $this->getFilterParam('training_city', null);
    if(!$training_city) {
      $training_city = $this->getTrainingCityVariable();
    }
    if($training_city != '' && $training_city != 'Cała Polska' && $training_city != 'Cały kraj') {
      $cache_query->setCity($training_city);
    }
    $benefits = $this->getBenefitsFilters();
    $selected_benefits = [];
    foreach($benefits as $benefit) {
      if($this->getFilterParam('filter_benefit_' . $benefit['id'], null)) {
        $selected_benefit = [];
        $selected_benefit['benefit_id'] = $benefit['id'];
        if(!empty($benefit['fields'])) {
          if(isset($benefit['fields']['minutes'])) {
            $benefit_minutes = $this->getFilterParam('filter_benefit_' . $benefit['id'] . '_' . $benefit['fields']['minutes'], null);
            if($benefit_minutes ) {
              if ($benefit_minutes != 'unlimited') {
                $selected_benefit['minutes'] = $benefit_minutes;
              } else {
                $selected_benefit['minutes'] = 'unlimited';
              }
            }
          }
          if(isset($benefit['fields']['days'])) {
            $benefit_days = $this->getFilterParam('filter_benefit_' . $benefit['id'] . '_' . $benefit['fields']['dates'], null);
            if($benefit_days) {
              if ($benefit_days != 'unlimited') {
                $benefit_days_array = array_values(explode('-', $benefit_days));
                $selected_benefit['days'] = $benefit_days_array;
              } else {
                $selected_benefit['days'] = ['unlimited', 'unlimited'];
              }
            }
          }
        }
        if ($benefit['id'] == 102) {
          $cache_query->setMaxUsers($this->getFilterParam('filter_max_users', null));
        } else if ($benefit['id'] == 1213) {
          $cache_query->setMinConfirmed($this->getFilterParam('filter_min_confirmed', null));
        } else if ($benefit['id'] == 15) {
          $cache_query->setMinUsers($this->getFilterParam('filter_min_users', null));
        }
        $selected_benefits[] = $selected_benefit;
      }
    }
    if(!empty($selected_benefits)) {
      $cache_query->setBenefits($selected_benefits);
    }
//     $filter_other_criteria = \Drupal::request()->request->all()['filter_other_criteria'];
    $filter_other_criteria = \Drupal::request()->request->all()['filter_other_criteria'];
    $cache_query->setOtherCriteria($filter_other_criteria);
    $tile_type = $this->getFilterParam('tile_type', $node->_npx_tile_type);
    if($tile_type == NpxProductClosed::getId() || $tile_type == NpxProductToOrder::getId()) {
      $cache_query->setDateFrom(null);
      $cache_query->setDateTo(null);
      $cache_query->setProductType($tile_type);
      $closed = $this->npxSearchCacheManager->findClosestDate($cache_query);
      return $closed;
    }
    $closest = $this->npxSearchCacheManager->findClosestDate($cache_query);

    if(!$closest) {
      $cache_query->setDateFrom(null);
      $cache_query->setDateTo(null);
      $closest = $this->npxSearchCacheManager->findClosestDate($cache_query);
    }

    if(!$closest && NpxSearchCacheManagerService::PROCESS_OLD_PRODUCTS) {
      $cache_query->setDateFrom('1970-01-01');
      $date_now_plus = new DrupalDateTime('now');
      $date_now_plus->modify('-1 day');
      $date_now_plus->setTimezone(new \DateTimezone(DateTimeItemInterface::STORAGE_TIMEZONE));
      $formatted_now_plus = $date_now_plus->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);
      $cache_query->setDateTo($formatted_now_plus);
      $cache_query->setOrderBy('date');
      $cache_query->setOrderDirection('DESC');

      $closest = $this->npxSearchCacheManager->findClosestDate($cache_query, 'DESC');
    }

    return $closest;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getBargainsDates()
   */
  public function getBargainsDates(NodeInterface $node): array {
    if(is_null($node->id())) {
      return [];
    }

    $closest_price = $this->getClosestDatePriceInfo($node);
    $closest_data = $this->getClosestDateInfo($node);
    $bargains = [];

    $quantity = (int) $this->getFilterParam('filter_quantity', 1);
    $exclude_prepaid = $this->getFilterParam('filter_prepaid_disabled', false);
    $price_type = $this->getFilterParam('filter_price_type', 'brutto');

    if(!($quantity > 0)) {
      $quantity = 1;
    }

    $products = $this->npxTrainingManager->getTrainingDatesByTrainingId($node->id());
    foreach($products as $product) {
      if(isset($closest_data['id']) && ($product->id() != $closest_data['id'])) {
        $price_info = $this->npxDiscountCalculator->getPriceInfo($product, $quantity);
        if(
          (isset($price_info['discounted']) && isset($closest_price['discounted'])) &&
          $price_info['discounted'] < $closest_price['discounted']
        ) {
          if($product->get('npx_event_date')[0]->value) {
            $start_date = NpxDateUtils::cutDateString($product->get('npx_event_date')[0]->value);
            $end_date = NpxDateUtils::cutDateString($product->get('npx_event_date')[count($product->get('npx_event_date'))-1]->end_value);
            $date_display = NpxDateUtils::prepareDateDisplay($start_date, $end_date, false, true);
          } else {
            $date_display = 'Termin na zamówienie';
          }
          $product_data = $this->getDateInfo($product, $node);
          $product_data['date_display'] = $date_display;
          $price_info = $this->npxDiscountCalculator->getPriceInfo($product, $quantity);
          $price_info['price_type'] = $price_type;
          $price_info['quantity'] = $quantity;
          $price_info['exclude_prepaid'] = $exclude_prepaid;
          $bargains[] = [
              "data" => $product_data,
              "price" => $price_info,
          ];
        }
      }
    }
    return $bargains;
  }

  public function getClosestDateInfo(NodeInterface $node): array {
    $product = $this->getClosestDate($node);
    $date_info = [];
    if($product != null) {
      $date_info = $this->getDateInfo($product, $node);
    }
    return $date_info;
  }

  public function getDateInfo(Product $product, NodeInterface $node): array {
    $date_info = [];
    if($product instanceof Product) {
      $field_city = null;
      $referenced_entities = $product->get('field_city')->referencedEntities();
      $city_term = reset($referenced_entities);
      if($city_term) {
        $city = $city_term->getName();
        if ($city) {
          $field_city = $city;
        }
      }

      $field_room = $product->get('field_room')->first();
      $room = null;
      if (!is_null($field_room) && $field_room->get('entity') && $field_room->get('entity')->getTarget()) {
        $room = $field_room->get('entity')->getTarget()->getValue();
        $room = \Drupal::entityTypeManager()->getStorage('node')->load($room->id());
      }
      $id = $product->id();
      if($room instanceof NodeInterface) {
        /** @var \Drupal\address\Plugin\Field\FieldType\AddressFieldItemList $locations */
        $locations = $room->get('field_room_address');
          /** @var \Drupal\address\Plugin\Field\FieldType\AddressItem $address */
        $address = $locations->get(0);
        if($address instanceof AddressItem && is_null($field_city)) {
          $field_city = $address->getLocality();
        }
      }
      $entities = $product->get('field_section_09')->referencedEntities();
      $benefits = null;
      foreach($entities as $entity) {
        $pars = $entity->get('field_benefit');
        foreach($pars as $par) {
          $bid = $par->getValue() ['target_id'];
          if($bid != 15 && $bid != 1213) {
            $text = "";
            switch ($bid) {
              case 12 :
                $text = $this->calculateMinutes($product->get('field_individual_minutes'));
                $text = $text . $this->calculateDays($product->get('field_individual_days'));
                break;
              case 13 :
                $text = $this->calculateDays($product->get('field_elearning_days'));
                break;
              case 17 :
                $text = $this->calculateMinutes($product->get('field_contact_phone_minutes'));
                $text = $text . $this->calculateDays($product->get('field_contact_phone_days'));
                break;
              case 78 :
                $text = $this->calculateDays($product->get('field_access_days'));
                break;
              case 79 :
                $text = $this->calculateDays($product->get('field_contact_email_days'));
                break;
              case 102 :
                $text = $this->calculateMaxUsers($node->get('field_max_users'));
                break;
              case 1213 :
                $text = ": " . $node->get('field_numeric_list_2')->value;
                break;
              default :
                break;
            }
            $benefits [] = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->load($bid)->name->value . $text;
          }
        }
      }

      $all_dates = [];
      $i = 0;
      foreach($product->get(TrainingDateFields::EVENT_DATE) as $p) {
        if(!is_null($p->start_date) && !is_null($p->end_date)) {
          /* start_date & end_date for events' RDFa */
          $all_dates[$i]['start_date'] = $p->start_date->format('Y-m-d');
          $all_dates[$i]['end_date'] = $p->end_date->format('Y-m-d');

          $start_date = $this->dateFormatter->format($p->start_date->getTimestamp(), 'custom', 'Y-m-d');
          $end_date = $this->dateFormatter->format($p->end_date->getTimestamp(), 'custom', 'Y-m-d');
          $all_dates[$i]['title'] = NpxDateUtils::prepareDateDisplay($start_date, $end_date);
          $all_dates[$i]['year'] = NpxDateUtils::getYear($start_date);
        }
        $i++;
      }

      if($product->bundle() == NpxProductToOrder::getId()) {
        $all_dates[] = [
          'start_date' => null,
          'end_date' => null,
          'title' => 'Termin na zamówienie',
          'year' => null,
        ];
      } elseif($product->bundle() == NpxProductClosed::getId()) {
        $all_dates[] = [
          'start_date' => null,
          'end_date' => null,
          'title' => 'Szkolenie zamknięte',
          'year' => null,
        ];
      }
//       $start_date = $this->dateFormatter->format($product->get(TrainingDateFields::EVENT_DATE)->start_date->getTimestamp(), 'custom', 'Y-m-d');
//       $end_date = $this->dateFormatter->format($product->get(TrainingDateFields::EVENT_DATE)->end_date->getTimestamp(), 'custom', 'Y-m-d');

      $date_info = [
          'all_dates' => $all_dates,
          'benefits' => $benefits,
          'time_info' => [
              'days' => (int) $node->get(NpxTrainingFields::NUMBER_OF_DAYS)->value,
              'days_label' => $this->getDaysLabel($node->get(NpxTrainingFields::NUMBER_OF_DAYS)->value),
              'hours' => (int) $node->get(NpxTrainingFields::NUMBER_OF_HOURS)->value,
          ],
          'id' => $id,
          'field_city' => $field_city,
          'guarantee' => $this->getGuaranteeLabel($product->get(TrainingDateFields::DATE_GUARANTEE)->value),
          'users' => [
              'min' => $node->get(NpxTrainingFields::MIN_USERS)->value,
              'max' => $node->get(NpxTrainingFields::MAX_USERS)->value,
          ],
      ];
    }

    return $date_info;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getCategoryTreeFilter()
   */
  public function getCategoryTreeFilter(): array {
    $terms = $this->loadAllTerms(NpxVocabularies::TRAINING_CATEGORY, false);

    foreach($terms as $key => $value) {
      if($terms[$key]->status == 0) {
        unset($terms[$key]);
      }
    }

    $options = [];
    foreach($terms as $term) {
      $options[$term->tid] = $term->name;
    }

    return $options;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getCategoryTreeFilterWidget()
   */
  public function getCategoryTreeFilterWidget(array $selected = null): array {
    $terms = $this->loadAllTerms(NpxVocabularies::TRAINING_CATEGORY, false);
    foreach($terms as $key => $value) {
      if($terms[$key]->status == 0) {
        unset($terms[$key]);
      }
    }
    $tree = [];

    foreach($terms as $term) {
      $this->buildTaxonomyTree($tree, $term, NpxVocabularies::TRAINING_CATEGORY, $selected);
    }
    return array_values($tree);
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getRegionTreeFilter()
   */
  public function getRegionTreeFilter(): array {
    $terms = $this->loadAllTerms(NpxVocabularies::REGIONS, false);
    $options = [];

    foreach($terms as $term) {
      $options[$term->tid] = $term->name;
    }

    return $options;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getRegionTreeFilterWidget()
   */
  public function getRegionTreeFilterWidget(array $selected = null): array {
    $terms = $this->loadAllTerms(NpxVocabularies::REGIONS, false);

    $tree = [];

    foreach($terms as $term) {
      $this->buildTaxonomyTree($tree, $term, NpxVocabularies::REGIONS, $selected);
    }
    return array_values($tree);
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::loadNodes()
   */
  public function loadNodes(array $nids): array {
    $nodes = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);
    $out = [];
    foreach($nodes as $node) {
      //clone objects to set deifferent tile_type parameters
      $out[$node->id()] = clone $node;
    }
    return $out;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::doFullSearch()
   */
  public function doFullSearch(FormStateInterface $form_state, bool $closed_products = false): array {
    $nids = [];
    $old_nids = [];
    $closed_nids = [];
    $to_order_nids = [];
    $text_phrase = $form_state->getValue('text_phrase', $this->getTextPhraseVariable());
    $text_nids = null;
    $text_category_nids = null;
    if($text_phrase != '') {
      $nids = $this->doTextSearch($text_phrase);
      $text_nids = $nids;
      if(empty($nids)) {
        return [ 'nids' => [], 'old_nids' => [], 'closed_nids' => [], 'to_order_nids' => [] ];
      }
    }

    $training_category = $this->getTrainingCategoryParam();
    $product_type = $form_state->getValue('product_type', $this->getProductTypeVariable());
    $training_type = $form_state->getValue('training_type', $this->getTrainingTypeVariable());

//     kint($product_type);

    $query = $this->entityTypeManager->getStorage('node')->getQuery()->accessCheck(false);
    $query->condition('type', 'npx_training');
    $query->condition('status', 1);

    if(!empty($nids)) {
      $query->condition('nid', $nids, 'IN');
    }
//     if($training_type != '') {
//       $query->condition(NpxTrainingFields::TRAINING_TYPE, $training_type);
//     }
    if($training_category instanceof TermInterface) {
      $cat_tids = $this->getTaxonomyTreeNids(NpxVocabularies::TRAINING_CATEGORY, $training_category->id());
      $query->condition(NpxTrainingFields::TRAINING_CATEGORY, $cat_tids, 'IN');
      $text_category_nids = array_values($query->accessCheck(FALSE)->execute());
    } else {
      $filter_category = $form_state->getValue('filter_category', null);
      $category_id = $_GET['category'];
      $filter_category[$category_id] = $category_id;

      $categories = $this->getCategoryTreeFilterWidget($form_state->getValue('filter_category'));
      foreach ($categories as $category) {
        if ($category->id == $category_id) {
          foreach ($category->children as $child) {
            $child->state = true;
            $filter_category[$child->id]=$child->id;
            foreach ($child->children as $grandchild) {
              $grandchild->state = true;
              $filter_category[$grandchild->id]=$grandchild->id;
            }
          }
        }
      }

      if($filter_category) {
        $selected_categories = $this->getSelectedOnlyCheckboxes($filter_category);
        if(!empty($selected_categories)) {
          $query->condition(NpxTrainingFields::TRAINING_CATEGORY, $selected_categories, 'IN');
          $text_category_nids = array_values($query->accessCheck(FALSE)->execute());
        }
      }
    }

    if($form_state->getValue('filter_benefit_15', null)) {
      $filter_min_users = $form_state->getValue('filter_min_users', null);
    } else {
      $filter_min_users = null;
    }
    if($form_state->getValue('filter_benefit_1213', null)) {
      $filter_min_confirmed = $form_state->getValue('filter_min_confirmed', null);
    } else {
      $filter_min_confirmed = null;
    }
    if($form_state->getValue('filter_benefit_102', null)) {
      $filter_max_users = $form_state->getValue('filter_max_users', null);
    } else {
      $filter_max_users = null;
    }

    $filter_time = $form_state->getValue('filter_time', null);

    if($filter_time) {
      $selected_time = $this->getSelectedOnlyCheckboxes($filter_time);

      if(!empty($selected_time)) {
        $filter_time_group = $query->orConditionGroup();

        foreach($selected_time as $time_val) {
          switch ($time_val) {
            case '2h':
              $filter_time_group->condition(NpxTrainingFields::NUMBER_OF_HOURS, 2, '<=');
              break;
            case '4h':
              $filter_time_group->condition(NpxTrainingFields::NUMBER_OF_HOURS, 4, '<=');
              break;
            case '1d':
              $filter_time_group->condition(NpxTrainingFields::NUMBER_OF_DAYS, 1, '<=');
              break;
            case '2d':
              $filter_time_group->condition(NpxTrainingFields::NUMBER_OF_DAYS, 2, '<=');
              break;
            case '3d':
              $filter_time_group->condition(NpxTrainingFields::NUMBER_OF_DAYS, 3, '<=');
              break;
            case '4d':
              $filter_time_group->condition(NpxTrainingFields::NUMBER_OF_DAYS, 4, '<=');
              break;
            case 'more':
              $filter_time_group->condition(NpxTrainingFields::NUMBER_OF_DAYS, 4, '>');
              break;
          }
        }

        $query->condition($filter_time_group);
      }
    }


    $order_by = $form_state->getValue('sort_by', 'date');
    $order_direction = $form_state->getValue('sort_order', 'ASC');

    $order_by_field_name = $order_by ? $this->getSortFieldName($order_by) : null;

    if($order_by_field_name) {
      $query->sort($order_by_field_name, $order_direction);
    }

    $nids = array_values($query->accessCheck(FALSE)->execute());

//     $this->createTestProducts($nids, 4);

    $filter_date_from = $form_state->getValue('filter_date_from', null);
    $filter_date_to = $form_state->getValue('filter_date_to', null);

    $filter_price_min = $form_state->getValue('filter_price_min', null);
    $filter_price_max = $form_state->getValue('filter_price_max', null);

    $cache_query = new NpxSearchCacheQuery();

    $cache_query->setOrderBy($order_by);
    $cache_query->setOrderDirection($order_direction);

    if($training_type != '') {
      $cache_query->setTrainingType($training_type);
    }

    if(!empty($nids)) {
      $cache_query->setNids($nids);
    } else {
      return [ 'nids' => [], 'old_nids' => [], 'closed_nids' => [], 'to_order_nids' => [] ];
    }

    if($filter_date_from) {
      $cache_query->setDateFrom($filter_date_from);
    } else {
      $date_now = new DrupalDateTime('now');
      $date_now->setTimezone(new \DateTimezone(DateTimeItemInterface::STORAGE_TIMEZONE));
      $formatted_now = $date_now->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);
      $cache_query->setDateFrom($formatted_now);
    }
    if($filter_date_to) {
      $cache_query->setDateTo($filter_date_to);
    }
    if($filter_price_min) {
      $cache_query->setPriceMin($filter_price_min);
    }
    if($filter_price_max) {
      $cache_query->setPriceMax($filter_price_max);
    }

    if($filter_min_users) {
      $cache_query->setMinUsers($filter_min_users);
    }
    if($filter_min_confirmed) {
      $cache_query->setMinConfirmed($filter_min_confirmed);
    }
    if($filter_max_users) {
      $cache_query->setMaxUsers($filter_max_users);
    }

    $benefits = $this->getBenefitsFilters();
    $selected_benefits = [];
    foreach($benefits as $benefit) {
      if($form_state->getValue('filter_benefit_' . $benefit['id'], null)) {
        $selected_benefit = [];
        $selected_benefit['benefit_id'] = $benefit['id'];

        if(!empty($benefit['fields'])) {
          if(isset($benefit['fields']['minutes'])) {
            $benefit_minutes = $form_state->getValue('filter_benefit_' . $benefit['id'] . '_' . $benefit['fields']['minutes'], null);

            if ($benefit_minutes) {
              if ($benefit_minutes != 'unlimited') {
                $selected_benefit['minutes'] = $benefit_minutes;
              } else {
                $selected_benefit['minutes'] = 'unlimited';
              }
            }
          }
          if(isset($benefit['fields']['days'])) {
            $benefit_days = $form_state->getValue('filter_benefit_' . $benefit['id'] . '_' . $benefit['fields']['days'], null);
            if($benefit_days) {
              if ($benefit_days != 'unlimited') {
                $benefit_days_array = array_values(explode('-', $benefit_days));
                $selected_benefit['days'] = $benefit_days_array;
              } else {
                $selected_benefit['days'] = ['unlimited', 'unlimited'];
              }
            }
          }
        }
        $selected_benefits[] = $selected_benefit;
      };
    }
    if(!empty($selected_benefits)) {
      $cache_query->setBenefits($selected_benefits);
    }
    $filter_other_criteria = $form_state->getValue('filter_other_criteria', null);

    if($filter_other_criteria) {
      $selected_criteria = $this->getSelectedOnlyCheckboxes($filter_other_criteria);
      $cache_query->setOtherCriteria($selected_criteria);
    }

    $filter_quantity = (int) $form_state->getValue('filter_quantity', 1);
    $filter_region = $form_state->getValue('filter_region', null);
    $training_city = $form_state->getValue('training_city', null);
    if(!$training_city) {
      $training_city = $this->getTrainingCityVariable();
    }

    $cache_query->setQuantity($filter_quantity);

    if($training_city && $training_city != 'Cała Polska' && $training_city != 'Cały kraj') {
      $cache_query->setCity($training_city);
    }

    if ($filter_region) {
      $selected_regions = $this->getSelectedOnlyCheckboxes($filter_region);
      if (!empty($selected_regions) && !in_array('1228', $selected_regions)) {
        $cache_query->setRegion($selected_regions);
      }
    }

    $nids_before_cache = $nids;
    if($cache_query->isQuerySet()) {
      if($this->isOpenProductOptionEnabled($product_type)) {
        //OPEN Products
        $cache_query->setProductType(NpxProductOpen::getId());
        $nids = $this->npxSearchCacheManager->findTrainingNids($cache_query);
      } else {
        $nids = [];
      }
    }

    if($text_nids) {
      if($text_category_nids) {
        $text_category_nids = array_intersect($text_nids, $text_category_nids);
      } else {
        $text_category_nids = $text_nids;
      }
    }

    if($this->isToOrderProductOptionEnabled($product_type)) {
      // TO_ORDER PRODUCTS
//      $cache_query->setNids($text_nids);
      $cache_query->setNids($text_category_nids);
      $cache_query->setDateFrom(null);
      $cache_query->setDateTo(null);
      $cache_query->setProductType(NpxProductToOrder::getId());
      $to_order_nids = $this->npxSearchCacheManager->findTrainingNids($cache_query);
      $cache_query->setProductType(null);
    }

    if($this->isClosedProductOptionEnabled($product_type)) {
      // CLOSED PRODUCTS
//      $cache_query->setNids($text_nids);
      $cache_query->setNids($text_category_nids);
      $cache_query->setDateFrom(null);
      $cache_query->setDateTo(null);
      $cache_query->setProductType(NpxProductClosed::getId());
      $closed_nids = $this->npxSearchCacheManager->findTrainingNids($cache_query);
      $cache_query->setProductType(null);
    }

    // OLD PRODUCTS
    if(NpxSearchCacheManagerService::PROCESS_OLD_PRODUCTS && $this->isOpenProductOptionEnabled($product_type)) {
      $cache_query->setNids(array_diff($nids_before_cache, $nids));
      if(!$filter_date_from) {
        $cache_query->setDateFrom('1970-01-01');
      }
      $date_now_minus = new DrupalDateTime('now');
      $date_now_minus->modify('-1 day');
      $date_now_minus->setTimezone(new \DateTimezone(DateTimeItemInterface::STORAGE_TIMEZONE));
      $formatted_now_minus = $date_now_minus->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);
      $cache_query->setDateTo($formatted_now_minus);

      if($order_by == 'date') {
        if($order_direction ==  'ASC') {
          $cache_query->setOrderDirection('DESC');
          if($order_by == 'date') {
            if($order_direction ==  'ASC') {
              $cache_query->setOrderDirection('DESC');
            } else {
              $cache_query->setOrderDirection('ASC');
            }
          }

          if($cache_query->isQuerySet()) {
            $old_nids = $this->npxSearchCacheManager->findTrainingNids($cache_query);
          }

        } else {
          $cache_query->setOrderDirection('ASC');
        }
      }

      if($cache_query->isQuerySet()) {
        $old_nids = $this->npxSearchCacheManager->findTrainingNids($cache_query);
      }
    }
    return [
      'nids' => $nids,
      'old_nids' => $old_nids,
      'closed_nids' => $closed_nids,
      'to_order_nids' => $to_order_nids,
    ];
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::searchCitiesByName()
   */
  public function searchCitiesByName(string $name): array {
    return $this->npxSearchCacheManager->getCachedCities($name);
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getTrainingTypeOptions()
   */
  public function getTrainingTypeOptions(): array {
    $options = [];
    $types = $this->npxSearchManager->getAllTrainingTypes();

    foreach($types as $type) {
      $options[$type->tid] = $type->name;
    }

    asort($options);

    return $options;
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getProductTypeOptions()
   */
  public function getProductTypeOptions(): array {
    return [
      NpxProductOpen::getId() => NpxProductOpen::getLabel(),
      NpxProductClosed::getId() => NpxProductClosed::getLabel(),
      NpxProductToOrder::getId() => NpxProductToOrder::getLabel(),
    ];
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::getOtherCriteria()
   */
  public function getOtherCriteria(): array {
    return $this->entityTypeManager->getStorage('taxonomy_term')->loadTree(NpxVocabularies::OTHER_CRITERIA);
  }

  /**
   * {@inheritDoc}
   * @see \Drupal\npx_search\NpxSearchManagerInterface::calculateDaysText()
   */
  public function calculateDaysText(int $days): string {
    if($days == 1) {
      $text = t("1 dzień");
    } elseif($days % 365 == 0) {
      $modulo = $days / 365;
      if($modulo == 1) {
        $text = t("1 rok");
      } elseif($modulo > 4) {
        $text = $modulo . t(" lat");
      } else {
        $text = $modulo . t(" lata");
      }
    } elseif($days % 30 == 0) {
      $modulo = $days / 30;
      if($modulo == 1) {
        $text = t("1 miesiąc");
      } elseif($modulo > 4) {
        $text = $modulo . t(" miesięcy");
      } else {
        $text = $modulo . t(" miesiące");
      }
    } elseif($days % 31 == 0) {
      $modulo = $days / 31;
      if($modulo == 1) {
        $text = t("miesiąc");
      } elseif($modulo > 4) {
        $text = $modulo . t(" miesięcy");
      } else {
        $text = $modulo . t(" miesiące");
      }
    } else {
      $text = $days . t(" dni");
    }
    return $text;
  }

  protected function createSearchCacheQuery(array $nids) {
    $order_by = $this->getFilterParam('sort_by', 'date');
    $order_direction = $this->getFilterParam('sort_order', 'ASC');
    $filter_date_from = $this->getFilterParam('filter_date_from', null);
    $filter_date_to = $this->getFilterParam('filter_date_to', null);
    $filter_price_min = $this->getFilterParam('filter_price_min', null);
    $filter_price_max = $this->getFilterParam('filter_price_max', null);
    $filter_quantity = (int) $this->getFilterParam('filter_quantity', 1);
    $filter_region = $this->getFilterParam('filter_region', null);
    $training_type = $this->getQueryParam('training_type', null);
    $training_city = $this->getQueryParam('training_city', null);
    if(!$training_city) {
      $training_city = $this->getTrainingCityVariable();
    }

    $cache_query = new NpxSearchCacheQuery();

    $cache_query->setOrderBy($order_by);
    $cache_query->setOrderDirection($order_direction);

    if(!empty($nids)) {
      $cache_query->setNids($nids);
    } else {
      return [];
    }

    if($filter_date_from) {
      $cache_query->setDateFrom($filter_date_from);
    } else {
      $date_now = new DrupalDateTime('now');
      $date_now->setTimezone(new \DateTimezone(DateTimeItemInterface::STORAGE_TIMEZONE));
      $formatted_now = $date_now->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);
      $cache_query->setDateFrom($formatted_now);
    }
    if($filter_date_to) {
      $cache_query->setDateTo($filter_date_to);
    }
    if($filter_price_min) {
      $cache_query->setPriceMin($filter_price_min);
    }
    if($filter_price_max) {
      $cache_query->setPriceMax($filter_price_max);
    }

    $cache_query->setQuantity($filter_quantity);
    if($training_city && $training_city != 'Cała Polska' && $training_city != 'Cały kraj') {
      $cache_query->setCity($training_city);
    }

    if($training_type) {
      $cache_query->setTrainingType($training_type);
    }

    if ($filter_region) {
      $selected_regions = $this->getSelectedOnlyCheckboxes($filter_region);
      if (!empty($selected_regions) && !in_array('1228', $selected_regions)) {
        $cache_query->setRegion($selected_regions);
      }
    }
//     $filter_other_criteria = \Drupal::request()->request->all()['filter_other_criteria'];
    $filter_other_criteria = \Drupal::request()->request->all()['filter_other_criteria'];
    $cache_query->setOtherCriteria($filter_other_criteria);

    $benefits = $this->getBenefitsFilters();
    $selected_benefits = [];
    foreach($benefits as $benefit) {
      if($this->getFilterParam('filter_benefit_' . $benefit['id'], null)) {
        $selected_benefit = [];
        $selected_benefit['benefit_id'] = $benefit['id'];
        if(!empty($benefit['fields'])) {
          if(isset($benefit['fields']['minutes'])) {
            $benefit_minutes = $this->getFilterParam('filter_benefit_' . $benefit['id'] . '_' . $benefit['fields']['minutes'], null);
            if($benefit_minutes ) {
              if ($benefit_minutes != 'unlimited') {
                $selected_benefit['minutes'] = $benefit_minutes;
              } else {
                $selected_benefit['minutes'] = 'unlimited';
              }
            }
          }
          if(isset($benefit['fields']['days'])) {
            $benefit_days = $this->getFilterParam('filter_benefit_' . $benefit['id'] . '_' . $benefit['fields']['dates'], null);
            if($benefit_days) {
              if ($benefit_days != 'unlimited') {
                $benefit_days_array = array_values(explode('-', $benefit_days));
                $selected_benefit['days'] = $benefit_days_array;
              } else {
                $selected_benefit['days'] = ['unlimited', 'unlimited'];
              }
            }
          }
        }
        if ($benefit['id'] == 102) {
          $cache_query->setMaxUsers($this->getFilterParam('filter_max_users', null));
        } else if ($benefit['id'] == 1213) {
          $cache_query->setMinConfirmed($this->getFilterParam('filter_min_confirmed', null));
        } else if ($benefit['id'] == 15) {
          $cache_query->setMinUsers($this->getFilterParam('filter_min_users', null));
        }
        $selected_benefits[] = $selected_benefit;
      }
    }
    if(!empty($selected_benefits)) {
      $cache_query->setBenefits($selected_benefits);
    }
    return $cache_query;
  }

protected function getSelectedOnlyCheckboxes($array): array {
    if (!is_array($array)) {
        return [];
    }

    $result = [];
    foreach ($array as $key => $value) {
        // Skip null values
        if (is_null($value)) {
            continue;
        }

        // Convert to string for comparison
        $value = (string)$value;

        // Only include non-zero values
        if ($value !== '0' && $value !== '') {
            $result[] = (string)$key;
        }
    }

    return array_values(array_filter($result));
}

  protected function getTaxonomyTreeNids($vid, $parent) {
    $terms = $this->entityTypeManager->getStorage('taxonomy_term')->loadTree($vid, $parent);
    $tids = [];

    foreach($terms as $term) {
      $tids[] = $term->tid;
    }

    return $tids;
  }

  protected function buildTaxonomyTree(&$tree, $object, $vocabulary, &$selected) {
    if($object->depth != 0) {
      return;
    }

    $tree[$object->tid] = (object) [
          'id' => $object->tid,
          'text' => $object->name,
          'children' => [],
          'state' => [
              'selected' => ($selected != null && $selected[$object->tid] == $object->tid),
          ],
    ];
    $object_children = &$tree[$object->tid]->children;

    $children = $this->entityTypeManager->getStorage('taxonomy_term')->loadChildren($object->tid);
    if(!$children) {
      return;
    }

    $child_tree_objects = $this->entityTypeManager->getStorage('taxonomy_term')->loadTree($vocabulary, $object->tid);

    foreach($children as $child) {
      foreach($child_tree_objects as $child_tree_object) {
        if($child_tree_object->tid == $child->id()) {
          $this->buildTaxonomyTree($object_children, $child_tree_object, $vocabulary, $selected);
        }
      }
    }
    $tree[$object->tid]->children = array_values($tree[$object->tid]->children);
  }

  protected function getBenefitRelatedFieldData(TermInterface $term): array {
    $data = [];
    foreach($term->get('field_npx_related_field')->getValue() as $value) {
      $val = $value['value'];
      $machine_name = $val;
      if(strpos($val, '|')) {
        $name_values = explode('|', $val);
        $machine_name = $name_values[0];
      }
      if(preg_match("/_days$/", $machine_name) === 1) {
        $data['days'] = $machine_name;
      } elseif(preg_match("/_minutes$/", $machine_name) === 1) {
        $data['minutes'] = $machine_name;
      } elseif(preg_match("/_min_users$/", $machine_name) === 1) {
        $data['min_users'] = $machine_name;
      } elseif(preg_match("/_max_users$/", $machine_name) === 1) {
        $data['max_users'] = $machine_name;
      } elseif(preg_match("/_numeric_list_2$/", $machine_name) === 1) {
        $data['confirmed_min'] = $machine_name;
      }
    }

    return $data;
  }

  protected function getQueryParam(string $name, $default = '') {
    $variables = UrlHelper::filterQueryParameters($this->request->getCurrentRequest()->query->all());

    return $variables[$name] ?? $default;
  }

  protected function getFilterParam(string $name, $default = '') {
    if ($name === 'filter_region' ||
        $name === 'filter_category' ||
        $name === 'filter_other_criteria') {
        return \Drupal::request()->request->all()[$name] ?? $default;
    }
    return \Drupal::request()->request->get($name, $default);// TODO dependency injection does not work properly.
//     return $this->request->getCurrentRequest()->get($name, $default);
  }

  protected function loadTerm(int $tid): ?TermInterface {
    return $this->entityTypeManager->getStorage('taxonomy_term')->load($tid);
  }

  protected function loadAllTerms(string $vid, bool $load_entities = false): array {
    return $this->entityTypeManager->getStorage('taxonomy_term')->loadTree($vid, 0, NULL, $load_entities);
  }

  protected function loadNode(int $nid): ?NodeInterface {
    return $this->entityTypeManager->getStorage('node')->load($nid);
  }

  protected function getGuaranteeLabel($value): ?string {
    switch ($value) {
      case 'confirmed':
        return '<span class="n-icon-confirmed">&nbsp;</span><span class="date-label">Termin</span> potwierdzony';
      case 'guaranteed':
        return '<span class="n-icon-guaranteed">&nbsp;</span><span class="date-label">Termin</span> gwarantowany';
      default:
        return null;
    }
  }

  protected function getLowStockLabel($value): ?string {
    switch ($value) {
      case 3:
      case 2:
        return 'Spiesz się pozostały tylko ' . $value . ' miejsca!';
      case 1:
        return 'Spiesz się pozostało tylko ' . $value . ' miejsce!';
      default:
        return null;
    }
  }

  protected function getDaysLabel($number) {
    $label = 'dzień';

    if($number > 1) {
      $label = 'dni';
    }

    return $label;
  }

  /*
   * Parse entity:[entity_type]:[entity_id]:[language_code] pattern
   */

  protected function parseResultItemUri(string $uri): string {
    $data = explode(':', $uri);
    $data = explode('/', $data[1]);

    return $data[1];
  }

  protected function getSortFieldName(string $option): ?string {
    switch ($option) {
      case 'rating':
        return 'field_average_ranking';
      case 'min_users':
        return 'field_min_users';
      case 'max_users':
        return 'field_max_users';
      case 'price':
      case 'date':
      default:
        return null;
    }
  }

  protected function isOpenProductOptionEnabled($product_type_filter): bool {
    return $this->isProductOptionEnabled($product_type_filter, NpxProductOpen::getId());
  }

  protected function isClosedProductOptionEnabled($product_type_filter): bool {
    return $this->isProductOptionEnabled($product_type_filter, NpxProductClosed::getId());
  }

  protected function isToOrderProductOptionEnabled($product_type_filter): bool {
    return $this->isProductOptionEnabled($product_type_filter, NpxProductToOrder::getId());
  }

  private function isProductOptionEnabled($product_type_filter, $product_id): bool {
    if(is_array($product_type_filter)) {
      return (count($product_type_filter) === 0 || in_array($product_id, $product_type_filter));
    } else {
      return ($product_type_filter === '' || $product_type_filter === $product_id);
    }
  }

  private function createTestProducts($nids, $quantity_per_node = 2) {
    foreach($nids as $nid) {
      for ($i = 0; $i < $quantity_per_node; $i++) {
        $this->createTestProduct($nid, $i);
      }
    }
  }

  private function createTestProduct($nid, $unique = 1) {
    $price = (float) (rand(1, 20) * 100);

    $discount = rand(1, 10) > 5 ? 1 : null;

    $variation = $this->entityTypeManager->getStorage('commerce_product_variation')->create([
        'type' => 'default',
        'sku' => 'trid/' . $nid . '/' . rand(1, 1000) . $unique,
        'price' => new Price($price, 'PLN'),
        'npx_discount' => $discount,
    ]);
    $variation->save();

    $year = '2021';
    $month = rand(1, 12);
    $day = rand(1, 25);
    $end_day = $day + rand(0, 3);

    $start = sprintf('%s-%s-%s', $year, $month, $day);
    $end = sprintf('%s-%s-%s', $year, $month, $end_day);

    $start_date = DrupalDateTime::createFromArray(['year' => $year, 'month' => $month, 'day' => $day]);
    $start_date->setTimezone(new \DateTimezone(DATETIME_STORAGE_TIMEZONE));
    $formatted_start = $start_date->format(DATETIME_DATETIME_STORAGE_FORMAT);

    $end_date = DrupalDateTime::createFromArray(['year' => $year, 'month' => $month, 'day' => $end_day]);
    $end_date->setTimezone(new \DateTimezone(DATETIME_STORAGE_TIMEZONE));
    $formatted_end = $end_date->format(DATETIME_DATETIME_STORAGE_FORMAT);

    $cities = [18, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77];

    $city = $cities[array_rand($cities)];

    $product = $this->entityTypeManager->getStorage('commerce_product')->create([
        'type' => 'default',
        'title' => sprintf('DEBUG %s - %s', $start, $end),
        'variations' => [$variation],
        'npx_event_date' => [
            'value' => $formatted_start,
            'end_value' => $formatted_end
        ],
        'npx_training' => $nid,
        'field_city' => $city
      ]);
    $product->save();
  }

  private function calculateMaxUsers($max) {
    if($max->count() == 0) {
      $text = t("bez limitu");
    } else {
      foreach($max as $m) {
        $text = $m->getValue()['value'];
      }
    }
    return ": " . $text;
  }

  private function calculateMinutes($minute) {
    if($minute->count() == 0) {
      $text = t("bez limitu");
    } else {
      foreach($minute as $m) {
        $text = $m->getValue()['value'];
        if($text % 60 == 0) {
          $modulo = $text / 60;
          $text = $modulo . t(" godz.");
        } else {
          $text = $text . t(" min.");
        }
      }
    }
    return t(" trwający: ") . $text;
  }

  private function calculateDays($day) {
    if($day->count() == 0) {
      $text = t("bez limitu");
    } else {
      foreach($day as $d) {
	$text = $d->getValue()['value'];
        $text = $this->calculateDaysText($text);
      }
    }
    return t(", ważny przez: ") . $text;
  }

}
