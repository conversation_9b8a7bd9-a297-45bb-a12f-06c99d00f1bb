(function ($, Drupal, drupalSettings) {
  ("use strict");

  const TOAST_CONTAINER_ID = "toast-container";

  function ensureToastContainer() {
    if (!document.getElementById(TOAST_CONTAINER_ID)) {
      $("body").append(
        `<div id="${TOAST_CONTAINER_ID}" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1080;"></div>`
      );
    }
  }

  function showToast(title, body, timeout = 3000) {
    ensureToastContainer();
    const rand = Math.round(Math.random() * 100000);
    const template = `
      <div id="toast--${rand}" class="toast align-items-center" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true">
        <div class="toast-header">
          <strong class="me-auto">${title}</strong>
          <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">${body || ""}</div>
      </div>`;
    $("#" + TOAST_CONTAINER_ID).append(template);
    const el = document.getElementById("toast--" + rand);
    try {
      const toast = new bootstrap.Toast(el, { delay: timeout });
      toast.show();
    } catch (e) {
      $(el).addClass("show");
      setTimeout(() => $(el).remove(), timeout);
    }
    setTimeout(() => el && el.remove(), timeout + 500);
  }

  const SEL = {
    filtersRoot: ".npx-offer-filters",
    companyLinks: ".views-field-npx-offer-profile-company-views-field a",
    categoryFields: ".views-field-field-offer-category .field-content",
    pageTitle: ".block-page-title-block h1",
    viewItem: ".view-items",
    actionsField: ".views-field-npx_offer_actions_views_field",
    contactData: ".views-field-contact-data",
    dropdownLinks:
      ".views-field-npx_offer_actions_views_field .dropbutton-wrapper .dropbutton-widget li a, .npx-offer-return-label, .npx-offer-return-link",
    dropbuttonToggle:
      ".views-field-npx_offer_actions_views_field .dropbutton-toggle button",
    dropbuttonItem:
      ".views-field-npx_offer_actions_views_field .dropbutton-wrapper .dropbutton li a",
  };

  const ACTION_TYPES = {
    reject: {
      class: "npx-offer-reject",
      toastTitle: "Oferta odrzucona",
      toastBody: "Pomyślnie odrzucono ofertę",
    },
    return: {
      classReturn: ["npx-offer-return-label", "npx-offer-return-link"],
      toastTitle: "Oferta przywrócona",
      toastBody: "Pomyślnie przywrócono ofertę",
    },
    accept: {
      class: "npx-offer-accept",
      toastTitle: "Oferta zaakceptowana",
      toastBody: "Pomyślnie zaakceptowano ofertę",
    },
    contact: {
      class: "npx-offer-contact",
      toastTitle: "Kontakt z Partnerem",
      toastBody: "Kontakt z Partnerem",
    },
  };

  function restoreScrollY(targetY, attempts = 10) {
    if (attempts <= 0) return;
    requestAnimationFrame(() => {
      window.scrollTo({ top: targetY, behavior: "auto" });
      restoreScrollY(targetY, attempts - 1);
    });
  }

  function rebuildOfferActionsDropdown(entityId, $clickedLink) {
    $.ajax({
      url: "/npx_offer/ajax/rebuild-offer-actions/" + entityId,
      type: "GET",
      dataType: "json",
      success: function (response) {
        if (!response || !response.html) return;
        const $actionsField = $clickedLink.closest(SEL.actionsField);
        $actionsField.html(response.html);
        Drupal.attachBehaviors($actionsField.get(0));
      },
    });
  }

  function determineActionType($link) {
    if ($link.hasClass("npx-offer-reject")) return "reject";
    if (
      $link.hasClass("npx-offer-return-label") ||
      $link.hasClass("npx-offer-return-link")
    )
      return "return";
    if ($link.hasClass("npx-offer-accept")) return "accept";
    if ($link.hasClass("npx-offer-contact")) return "contact";
    return "default";
  }

  function updateProgressStepsOnAccept() {
    const $progressWrapper = $(".npx-progress-steps-wrapper");
    const $activeStep = $progressWrapper.find(".step--active").first();
    const $upcomingStep = $progressWrapper.find(".step--upcoming").first();
    if ($activeStep.length) {
      $activeStep
        .removeClass("step--active")
        .addClass("step--completed")
        .find(".step__number")
        .html("✓");
    }
    if ($upcomingStep.length) {
      $upcomingStep.removeClass("step--upcoming").addClass("step--active");
    }
  }

  function ensureContactContainer($actionsField) {
    if ($actionsField.find(SEL.contactData).length === 0) {
      $actionsField.append(
        $("<div>", {
          class:
            "views-field-contact-data color-bg-light-blue t2 text-align-left mx-3 mt-2 px-2",
          style: "display:none;",
        })
      );
    }
  }

  function isContactMarkup(data) {
    if (!data) return false;
    const html = String(data).trim();
    if (!html) return false;

    const tmp = document.createElement("div");
    tmp.innerHTML = html;

    const links = tmp.querySelectorAll('a[href^="mailto:"], a[href^="tel:"]');
    if (!links.length) return false;

    for (const a of links) {
      const rawHref = (a.getAttribute("href") || "").trim();
      if (!rawHref) continue;

      if (/^mailto:/i.test(rawHref)) {
        const email = rawHref.replace(/^mailto:\/*/i, "").split("?")[0];
        if (/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) return true;
      } else if (/^tel:/i.test(rawHref)) {
        const num = rawHref.replace(/^tel:\/*/i, "").replace(/[^\d+]/g, "");
        if (num.replace(/\D/g, "").length >= 5) return true;
      }
    }
    return false;
  }

  function showNoContactToast() {
    // CHANGED: unified title, added body text.
    showToast("Kontakt z Partnerem", "Brak danych kontaktowych");
  }

  function renderNoContact($container) {
    ensureToastContainer();
    if ($container && $container.length) {
      $container.addClass("d-none").hide();
    }
    // Title/body now handled inside showNoContactToast().
    showNoContactToast();
  }

  function fetchContactData(entityId, $clickedLink, scrollY) {
    const inquiryId = drupalSettings?.npx_offer?.inquiryId;
    if (!inquiryId) {
      showNoContactToast();
      return;
    }
    const url = `/npx_offer/get_contact_person/${inquiryId}/${entityId}`;

    const $actionsField = $(
      `${SEL.actionsField} [data-pid="${entityId}"]`
    ).closest(SEL.actionsField);

    // Always (re)create container after dropdown rebuild.
    ensureContactContainer($actionsField);

    let $contactContainer = $actionsField.find(SEL.contactData);

    // Spinner
    $contactContainer
      .removeClass("d-none")
      .html(
        '<div class="text-center py-2"><div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>'
      )
      .show();

    $.ajax({
      url,
      type: "GET",
      dataType: "html",
    })
      .done(function (data) {
        const $actionsField2 = $(
          `${SEL.actionsField} [data-pid="${entityId}"]`
        ).closest(SEL.actionsField);
        ensureContactContainer($actionsField2);
        const $contactContainer2 = $actionsField2.find(SEL.contactData);

        if (isContactMarkup(data)) {
          $contactContainer2.removeClass("d-none").html(data).show();
          showToast("Kontakt z Partnerem", "Wyświetlono kontakt z Partnerem.");
        } else {
          renderNoContact($contactContainer2);
        }
      })
      .fail(function () {
        const $actionsField2 = $(
          `${SEL.actionsField} [data-pid="${entityId}"]`
        ).closest(SEL.actionsField);
        const $contactContainer2 = $actionsField2.find(SEL.contactData);
        renderNoContact($contactContainer2);
      })
      .always(function () {
        setTimeout(() => restoreScrollY(scrollY), 100);
        setTimeout(
          () =>
            Drupal.behaviors.npx4growOffersChangingCounters.ajaxUpdateCounters(),
          400
        );
        setTimeout(
          () =>
            Drupal.behaviors.npx4growOffersChangingCounters.ajaxUpdateCounters(),
          700
        );
      });
  }

  /* ==========================================================================
     Behavior: Filters
     ========================================================================== */
  Drupal.behaviors.npxOfferListViewFilters = {
    attach: function (context) {
      if ($(context).find(SEL.filtersRoot).length) return;

      const uniqueCompanyValues = {};
      $(SEL.companyLinks, context).each(function () {
        const v = $(this).text().trim();
        if (v) uniqueCompanyValues[v] = true;
      });

      const $filtersDiv = $("<div>", {
        class:
          "npx-offer-filters text-align-left d-flex flex-column flex-sm-row",
      });

      if (Object.keys(uniqueCompanyValues).length >= 2) {
        const $wrapperDiv = $("<div>", {
          class: "company-filters-wrapper",
        }).append('<h3 class="mb-2">Filtruj po firmie</h3>');
        $.each(uniqueCompanyValues, function (value) {
          $wrapperDiv.append(
            $("<div>").append(
              $("<label>").append(
                $("<input>", {
                  type: "checkbox",
                  value,
                  checked: true,
                  class: "form-checkbox form-control",
                }),
                " " + value
              )
            )
          );
        });
        $filtersDiv.append($wrapperDiv);
      }

      if ($filtersDiv.children().length) {
        $(SEL.pageTitle, context).after($filtersDiv);
      }

      $filtersDiv.find('input[type="checkbox"]').on("change", function () {
        const value = $(this).val();
        const $targets = $(
          `${SEL.companyLinks}, ${SEL.categoryFields}`,
          context
        );
        const checked = $(this).is(":checked");
        $targets.each(function () {
          if ($(this).text().trim() === value) {
            $(this)
              .closest(SEL.viewItem)
              .css("display", checked ? "" : "none");
          }
        });
      });
    },
  };

  /* ==========================================================================
     Behavior: Comment submit
     ========================================================================== */
  Drupal.behaviors.npxOfferListViewComment = {
    attach: function (context) {
      once(
        "npx-offer-list-view-comment",
        "#offer-comment-submit-button",
        context
      ).forEach((button) => {
        const $button = $(button);
        const $form = $button.closest("form");
        const inquiryId = window.location.pathname.split("/").pop();

        $button.on("click", function (e) {
          e.preventDefault();
          const productId = $button.data("pid");
          const comment = $form.find('textarea[name="comment"]').val();

          $.ajax({
            url: "/offer/save_comment/" + inquiryId,
            type: "POST",
            contentType: "application/json",
            dataType: "json",
            data: JSON.stringify({ comment, product_id: productId }),
          })
            .done(function (response) {
              if (response.status === "success") {
                showToast(
                  Drupal.t("Sukces"),
                  Drupal.t("Komentarz został zapisany.")
                );
              } else {
                showToast(
                  Drupal.t("Błąd"),
                  Drupal.t("Wystąpił błąd podczas zapisywania komentarza.")
                );
              }
            })
            .fail(function () {
              showToast(
                Drupal.t("Błąd"),
                Drupal.t("Wystąpił błąd podczas zapisywania komentarza.")
              );
            });

          return false;
        });
      });
    },
  };

  /* ==========================================================================
     Behavior: Suboffer text color
     ========================================================================== */
  Drupal.behaviors.npxOfferListViewSubofferTextColor = {
    attach: function () {
      $(".suboffer-title-text").each(function () {
        const $this = $(this);
        const suboffer = $this.text().trim();
        $this.addClass(
          suboffer === "Ta oferta zawiera suboferty!"
            ? "text-red"
            : "text-violet"
        );
      });
    },
  };

  /* ==========================================================================
     Behavior: Actions (accept/reject/return/contact)
     ========================================================================== */
  Drupal.behaviors.npxOfferListViewActions = {
    attach: function (context) {
      once("npx-offer-list-view-actions", SEL.dropdownLinks, context).forEach(
        (element) => {
          $(element).on("click", function (e) {
            e.preventDefault();
            this.blur();

            const scrollY = window.scrollY;
            const $clickedLink = $(this);

            if (
              $clickedLink.hasClass("npx-offer-reject-label") ||
              $clickedLink.hasClass("npx-offer-contact-label") ||
              $clickedLink.hasClass("npx-offer-default")
            ) {
              return false;
            }

            const actionUrl = $clickedLink.attr("href");
            const entityId = $clickedLink.attr("data-pid");
            const actionType = determineActionType($clickedLink);

            $.ajax({
              url: actionUrl,
              type: "GET",
              dataType: "text",
            })
              .done(() => {
                handleActionSuccess(
                  actionType,
                  $clickedLink,
                  entityId,
                  scrollY
                );
              })
              .fail(() => {
                showToast("Błąd", "Akcja nie powiodła się");
              });
          });
        }
      );
    },
  };

  function handleActionSuccess(actionType, $clickedLink, entityId, scrollY) {
    const meta = ACTION_TYPES[actionType] || {
      toastTitle: "Akcja wykonana",
      toastBody: "",
    };

    if (actionType !== "contact" && meta.toastTitle) {
      showToast(meta.toastTitle, meta.toastBody);
    }

    if (actionType === "contact") {
      new Promise((resolve) => {
        rebuildOfferActionsDropdown(entityId, $clickedLink);
        setTimeout(resolve, 450); // slightly faster
      }).then(() => fetchContactData(entityId, $clickedLink, scrollY));
    }

    const $viewItem = $clickedLink.closest(SEL.viewItem);
    if ($viewItem.length) {
      switch (actionType) {
        case "reject":
          $viewItem.addClass("view-items-offer-rejected");
          Drupal.behaviors.npx4growOffersChangingCounters.moveToLastPosition(
            $viewItem
          );
          setTimeout(() => restoreScrollY(scrollY), 100);
          Drupal.behaviors.npx4growOffersEqualHeight.equalHeightRefresh();
          break;
        case "return":
          $viewItem.removeClass("view-items-offer-rejected");
          const $firstRejected = $(
            ".view-items.view-items-offer-rejected"
          ).first();
          if ($firstRejected.length) {
            $viewItem.detach().insertBefore($firstRejected);
          }
          setTimeout(() => restoreScrollY(scrollY), 100);
          $(SEL.actionsField).css("height", "84px");
          break;
        case "accept":
          $viewItem.addClass("view-items-offer-accepted");
          Drupal.behaviors.npx4growOffersChangingCounters.moveToFirstPosition(
            $viewItem
          );
          updateProgressStepsOnAccept();
          setTimeout(() => restoreScrollY(scrollY), 100);
          break;
        case "contact":
          break;
      }
    }

    if (["reject", "return", "accept", "contact"].includes(actionType)) {
      rebuildOfferActionsDropdown(entityId, $clickedLink);
    }
  }

  /* ==========================================================================
     Behavior: Setup contact container
     ========================================================================== */
  Drupal.behaviors.npxOfferListViewSetup = {
    attach: function (context) {
      once("npx-contact-containers-init", "body", context).forEach(() =>
        ensureToastContainer()
      );
      // Do NOT wrap in once per actionsField so recreated markup gets container again:
      $(SEL.actionsField, context).each(function () {
        ensureContactContainer($(this));
      });
    },
  };

  /* ==========================================================================
     Behavior: Sibling hover highlight
     ========================================================================== */
  Drupal.behaviors.npxOfferListViewSiblingHoverEffect = {
    attach: function (context) {
      once(
        "sibling-hover",
        ".view-inquiry-offers-list .view-items",
        context
      ).forEach((item) => {
        const $item = $(item);
        $item
          .on("mouseenter", function () {
            $item.prev(".view-items").addClass("sibling-on-hover");
            $item.next(".view-items").addClass("sibling-on-hover");
          })
          .on("mouseleave", function () {
            $(
              ".view-inquiry-offers-list .view-items.sibling-on-hover"
            ).removeClass("sibling-on-hover");
          });
      });
    },
  };
})(jQuery, Drupal, drupalSettings);
