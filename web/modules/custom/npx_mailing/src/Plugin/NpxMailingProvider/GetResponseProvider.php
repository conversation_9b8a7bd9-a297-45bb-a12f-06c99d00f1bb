<?php

declare(strict_types=1);

namespace <PERSON><PERSON>al\npx_mailing\Plugin\NpxMailingProvider;

use <PERSON><PERSON>al\Core\Config\ConfigFactoryInterface;
use <PERSON><PERSON>al\Core\Config\ImmutableConfig;
use <PERSON><PERSON><PERSON>\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON><PERSON>\npx_mailing\NpxMailingList;
use <PERSON><PERSON>al\npx_mailing\NpxMailingProviderPluginBase;
use Drupal\npx_mailing\NpxMailingSubscriber;
use Getresponse\Sdk\GetresponseClientFactory;
use Getresponse\Sdk\Client\GetresponseClient;
use Getresponse\Sdk\Operation\Contacts\CreateContact\CreateContact;
use Getresponse\Sdk\Operation\Model\CampaignReference;
use Getresponse\Sdk\Operation\Model\NewContact;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Plugin implementation of the npx_mailing_provider.
 *
 * @NpxMailingProvider(
 *   id = "getresponse",
 *   label = @Translation("GetResponse"),
 *   description = @Translation("GetResponse provider.")
 * )
 */
final class GetResponseProvider extends NpxMailingProviderPluginBase implements ContainerFactoryPluginInterface {
  private readonly ImmutableConfig $config;
  private readonly GetresponseClient $client;

  public function __construct(array $configuration, $plugin_id, $plugin_definition, ConfigFactoryInterface $config_factory) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->config = $config_factory->get('npx_mailing.get_response_settings');
    $api_key = $this->config->get('get_response_api_key');
    $this->client = GetresponseClientFactory::createWithApiKey($api_key);
  }

  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('config.factory')
    );
  }
  /**
   * @inheritdoc
   */
  public function subscribe(NpxMailingSubscriber $subscriber, NpxMailingList $list): bool {
    $campaign_id = $list->getListId();
    $email = $subscriber->getEmail();
    $name = $subscriber->getName();

    $campaignReference = new CampaignReference($campaign_id);
    $newContact = new NewContact($campaignReference, $email);
    $newContact->setName($name);
    $newContact->setDayOfCycle(0);

    $createContactOperation = new CreateContact($newContact);
    $response = $this->client->call($createContactOperation);

    if ($response->isSuccess()) {
      return true;
    }

    return false;
  }

  /**
  * @inheritdoc
  */
  public function subscribeMultiple(array $subscribers, NpxMailingList $list): void {
    /** @var \Drupal\npx_mailing\NpxMailingSubscriber $subscriber */
    foreach($subscribers as $subscriber) {
      $this->subscribe($subscriber, $list);
    }
  }
}

