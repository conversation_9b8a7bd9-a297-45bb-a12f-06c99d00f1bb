<?php

declare(strict_types=1);

namespace Drupal\npx_mailing;

use <PERSON><PERSON><PERSON>\Component\Plugin\Exception\PluginException;
use <PERSON><PERSON>al\Core\Config\ConfigFactoryInterface;
use Drupal\Core\Config\ImmutableConfig;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Dr<PERSON>al\npx_mailing\NpxMailingProviderPluginManager;

/**
 * @todo Add class description.
 */
final class NpxMailingManagerService {

  public const SERVICE_ID = 'npx_mailing.manager';

  private const DEFAULT_CATEGORY_CONFIG = 'npx_mailing_default_category';
  private const PROVIDER_CONFIG = 'npx_mailing_provider';

  private readonly ImmutableConfig $config;

  /**
   * Constructs a NpxMailingManagerService object.
   */
  public function __construct(
    private readonly NpxMailingProviderPluginManager $pluginManagerNpxMailingProvider,
    private readonly ConfigFactoryInterface $configFactory,
    private readonly EntityTypeManagerInterface $entityTypeManager,
  ) {
    $this->config = $configFactory->get('npx_mailing.settings');
  }

  /**
   * Subscribes to mailing list.
   *
   * @param NpxMailingSubscriber $subscriber
   * @param NpxMailingList $list
   * @return bool
   */
  public function subscribe(NpxMailingSubscriber $subscriber, NpxMailingList|null $list=null): bool {
    $mailing_provider = $this->getProviderInstance();

    if($mailing_provider instanceof NpxMailingProviderInterface) {
      if($list == null) {
        $list = $this->getDefaultList();
      }

      if($list instanceof NpxMailingList) {
        return $mailing_provider->subscribe($subscriber, $list);
      }
    }

    return false;
  }

  /**
   * Multiple subscribes to mailing list.
   *
   * @param array $subscribers array of NpxMailingSubscriber objects
   * @param NpxMailingList $list
   */
  public function subscribeMultiple(array $subscribers, NpxMailingList|null $list=null): void {
    foreach($subscribers as $subscriber) {
      if($subscriber instanceof NpxMailingSubscriber) {
        $this->subscribe($subscriber, $list);
      }
    }
  }

  private function getProviderInstance(): NpxMailingProviderInterface|null {
    $provider = null;
    $provider_id = $this->config->get(self::PROVIDER_CONFIG);

    try {
      $provider = $this->pluginManagerNpxMailingProvider->createInstance($provider_id);
    } catch(PluginException) {
      // do nothing
    }

    return $provider;
  }

  private function getDefaultList(): NpxMailingList|null {
    $tid = $this->config->get(self::DEFAULT_CATEGORY_CONFIG);
    /** @var \Drupal\taxonomy\Entity\Term $term */
    $term = $this->entityTypeManager->getStorage('taxonomy_term')->load($tid);

    if($term) {
      $list_id = $term->get(NpxMailingList::FIELD_LIST_ID)->value;

      return new NpxMailingList($list_id);
    }

    return null;
  }
}

