<?php

declare(strict_types=1);

namespace Drupal\npx_mailing;

/**
 * Interface for npx_mailing_provider plugins.
 */
interface NpxMailingProviderInterface {

  /**
   * Rerurns plugin id.
   *
   * @return string
   */
  public function id(): string;

  /**
   * Returns the translated plugin label.
   *
   * @return string
   */
  public function label(): string;

  /**
   * Returns the translated plugin description.
   *
   * @return string
   */
  public function description(): string;

  /**
   * Subscribe email to list.
   *
   * @param NpxMailingSubscriber $subscriber
   * @param NpxMailingList $list
   *
   * @return bool
   */
  public function subscribe(NpxMailingSubscriber $subscriber, NpxMailingList $list): bool;

   /**
   * Subscribe multiple emails to list.
   *
   * @param array $subscribers
   * @param NpxMailingList $list
   *
   * @return void
   */
  public function subscribeMultiple(array $subscribers, NpxMailingList $list): void;

}
