<?php

namespace Drupal\npx_mailing;

final class NpxMailingList {

  public const VOCABULARY_ID = 'npx_mailing_categories';
  public const FIELD_LIST_ID = 'field_external_list_id';

  private readonly string $listId;

  /**
   * Constructor
   *
   * @param string $list_id
   */
  public function __construct(string $list_id) {
    $this->setListId($list_id);
  }

  /**
   * Returns list ID
   *
   * @return NpxMailingList
   */
  public function getListId(): string {
    return $this->listId;
  }

  /**
   * Sets list ID
   *
   * @param string $list_id
   * @return NpxMailingList
   */
  public function setListId(string $list_id): NpxMailingList {
    $this->listId = $list_id;
    return $this;
  }
}

