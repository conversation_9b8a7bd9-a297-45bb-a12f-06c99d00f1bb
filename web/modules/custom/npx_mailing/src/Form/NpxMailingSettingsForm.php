<?php

declare(strict_types=1);

namespace Drupal\npx_mailing\Form;

use <PERSON><PERSON>al\Core\Entity\EntityTypeManager;
use <PERSON><PERSON>al\Core\Form\ConfigFormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\npx_mailing\NpxMailingList;
use <PERSON><PERSON>al\npx_mailing\NpxMailingManagerService;
use Drupal\npx_mailing\NpxMailingProviderPluginManager;
use Drupal\npx_mailing\NpxMailingSubscriber;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Configure Npx Mailing settings for this site.
 */
final class NpxMailingSettingsForm extends ConfigFormBase {

  public function __construct(
    private readonly NpxMailingProviderPluginManager $providerManager,
    private readonly EntityTypeManager $entityTypeManager
  ) {

  }

  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('plugin.manager.npx_mailing_provider'),
      $container->get('entity_type.manager')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId(): string {
    return 'npx_mailing_npx_mailing_settings';
  }

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames(): array {
    return ['npx_mailing.settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state): array {
    $providers = [];
    foreach ($this->providerManager->getDefinitions() as $id => $definition) {
      $providers[$id] = $definition['label'];
    }

    $config = $this->config('npx_mailing.settings');

    $form['npx_mailing'] = [
      '#type' => 'details',
      '#title' => 'Konfiguracja mailingu',
      '#open' => true,
    ];

    $form['npx_mailing']['npx_mailing_provider'] = [
      '#type' => 'select',
      '#title' => $this->t('Mailing provider'),
      '#options' => $providers,
      '#default_value' => $config->get('npx_mailing_provider'),
    ];

    $form['npx_mailing']['npx_mailing_default_category'] = [
      '#type' => 'select', // albo 'radios'
      '#title' => $this->t('Select default category'),
      '#options' => $this->getMailingCategoryOptions(),
      '#default_value' => $config->get('npx_mailing_default_category'),
      '#required' => TRUE,
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state): void {
    parent::validateForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state): void {
    $this->config('npx_mailing.settings')
      ->set('npx_mailing_provider', $form_state->getValue('npx_mailing_provider'))
      ->set('npx_mailing_default_category', $form_state->getValue('npx_mailing_default_category'))
      ->save();
    parent::submitForm($form, $form_state);
  }

  private function getMailingCategoryOptions(): array {
    $tids = $this->entityTypeManager->getStorage('taxonomy_term')->getQuery()->accessCheck(false)
      ->condition('vid', NpxMailingList::VOCABULARY_ID)
      ->condition('status', 1)
      ->execute();
    $terms = $this->entityTypeManager->getStorage('taxonomy_term')->loadMultiple($tids);

    $options = [];

    /** @var \Drupal\taxonomy\Entity\Term $term */
    foreach($terms as $term) {
      $options[$term->id()] = $term->getName();
    }

    return $options;
  }
}
