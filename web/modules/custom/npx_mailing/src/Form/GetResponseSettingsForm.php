<?php

declare(strict_types=1);

namespace Drupal\npx_mailing\Form;

use Drupal\Core\Form\ConfigFormBase;
use Drupal\Core\Form\FormStateInterface;

/**
 * Configure Npx Mailing settings for this site.
 */
final class GetResponseSettingsForm extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  public function getFormId(): string {
    return 'npx_mailing_get_response_settings';
  }

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames(): array {
    return ['npx_mailing.get_response_settings'];
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state): array {
    $config = $this->config('npx_mailing.get_response_settings');

    $form['get_response'] = [
      '#type' => 'details',
      '#title' => 'Konfiguracja GetResponse',
      '#open' => true,
    ];
    $form['get_response']['get_response_api_key'] = [
      '#type' => 'textfield',
      '#title' => $this->t('API Key'),
      '#default_value' => $config->get('get_response_api_key'),
      '#required' => true,
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state): void {
    parent::validateForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state): void {
    $this->config('npx_mailing.get_response_settings')
      ->set('get_response_api_key', $form_state->getValue('get_response_api_key'))
      ->save();
    parent::submitForm($form, $form_state);
  }

}
