<?php

declare(strict_types=1);

namespace Drupal\npx_mailing;

use <PERSON><PERSON><PERSON>\Core\Cache\CacheBackendInterface;
use <PERSON><PERSON><PERSON>\Core\Extension\ModuleHandlerInterface;
use <PERSON><PERSON>al\Core\Plugin\DefaultPluginManager;
use Drupal\npx_mailing\Annotation\NpxMailingProvider;

/**
 * NpxMailingProvider plugin manager.
 */
final class NpxMailingProviderPluginManager extends DefaultPluginManager {

  /**
   * Constructs the object.
   */
  public function __construct(\Traversable $namespaces, CacheBackendInterface $cache_backend, ModuleHandlerInterface $module_handler) {
    parent::__construct('Plugin/NpxMailingProvider', $namespaces, $module_handler, NpxMailingProviderInterface::class, NpxMailingProvider::class);
    $this->alterInfo('npx_mailing_provider_info');
    $this->setCacheBackend($cache_backend, 'npx_mailing_provider_plugins');
  }

}
