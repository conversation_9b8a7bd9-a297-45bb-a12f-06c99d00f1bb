<?php

namespace Drupal\npx_mailing;

final class NpxMailingSubscriber {
  private readonly string $email;
  private readonly string $name;

  public function __construct(string $email, string $name='') {
    $this->setEmail($email)->setName($name);
  }

  /**
   * Returns email
   *
   * @return string
   */
  public function getEmail(): string {
    return $this->email;
  }

  /**
   * Sets email
   *
   * @param string $email
   * @return NpxMailingSubscriber
   */
  public function setEmail(string $email): NpxMailingSubscriber {
    $this->email = $email;
    return $this;
  }

  /**
   * Returns name
   *
   * @return string
   */
  public function getName(): string {
    return $this->name;
  }

  /**
   * Sets name
   *
   * @param string $name
   * @return NpxMailingSubscriber
   */
  public function setName(string $name): NpxMailingSubscriber {
    $this->name = $name;
    return $this;
  }
}

