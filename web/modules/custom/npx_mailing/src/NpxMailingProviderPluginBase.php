<?php

declare(strict_types=1);

namespace <PERSON>upal\npx_mailing;

use <PERSON><PERSON><PERSON>\Component\Plugin\PluginBase;

/**
 * Base class for npx_mailing_provider plugins.
 */
abstract class NpxMailingProviderPluginBase extends PluginBase implements NpxMailingProviderInterface {

  /**
   * {@inheritdoc}
   */
  public function id(): string {
    return (string) $this->pluginDefinition['id'];
  }

  /**
   * {@inheritdoc}
   */
  public function label(): string {
    // Cast the label to a string since it is a TranslatableMarkup object.
    return (string) $this->pluginDefinition['label'];
  }

  /**
   * {@inheritdoc}
   */
  public function description(): string {
    // Cast the label to a string since it is a TranslatableMarkup object.
    return (string) $this->pluginDefinition['description'];
  }

}
