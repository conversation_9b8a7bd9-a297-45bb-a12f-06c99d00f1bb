{"name": "drupal-composer/drupal-project", "description": "Drupal 10 instance", "type": "project", "license": "GPL-2.0-or-later", "authors": [{"name": "", "role": ""}], "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}, {"type": "package", "package": {"name": "enyo/dropzone", "version": "v5.5.0", "type": "drupal-library", "source": {"url": "https://github.com/enyo/dropzone.git", "type": "git", "reference": "v5.5.0"}}}, {"type": "package", "package": {"name": "mozilla/pdf.js", "version": "v2.4.456", "type": "drupal-library", "dist": {"url": "https://github.com/mozilla/pdf.js/releases/download/v2.4.456/pdfjs-2.4.456-es5-dist.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "jquery-textfill/jquery-textfill", "version": "v0.6.0", "type": "drupal-library", "dist": {"url": "https://github.com/jquery-textfill/jquery-textfill/archive/refs/heads/master.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "almende/vis", "version": "v4.21.0", "type": "drupal-library", "source": {"url": "https://github.com/almende/vis.git", "type": "git", "reference": "v4.21.0"}}}, {"type": "package", "package": {"name": "davatron5000/fittext", "version": "1.2.0", "type": "drupal-library", "dist": {"url": "https://github.com/davatron5000/FitText.js/archive/refs/heads/master.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "kenwheeler/slick", "version": "1.8.1", "type": "drupal-library", "dist": {"url": "https://github.com/kenwheeler/slick/archive/v1.8.1.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "jackmoore/colorbox", "version": "1.6.4", "type": "drupal-library", "dist": {"url": "https://github.com/jackmoore/colorbox/archive/1.6.4.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "jedfoster/readmorejs", "version": "2.2.1", "type": "drupal-library", "dist": {"url": "https://github.com/jedfoster/Readmore.js/archive/2.2.1.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "owlcarousel2/owlcarousel2", "version": "2.3.4", "type": "drupal-library", "dist": {"url": "https://github.com/OwlCarousel2/OwlCarousel2/archive/2.3.4.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "fullcalendar/fullcalendar", "version": "5.3.2", "type": "drupal-library", "dist": {"url": "https://github.com/fullcalendar/fullcalendar/releases/download/v5.3.2/fullcalendar-5.3.2.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "fengyuanchen/jquery-cropper", "version": "v1.0.1", "type": "drupal-library", "source": {"url": "https://github.com/fengyuanchen/jquery-cropper.git", "type": "git", "reference": "v1.0.1"}}}, {"type": "package", "package": {"name": "vakata/jstree", "version": "3.3.10", "type": "drupal-library", "dist": {"url": "https://github.com/vakata/jstree/archive/3.3.10.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "garand/sticky", "version": "1.0.3", "type": "drupal-library", "dist": {"url": "https://github.com/garand/sticky/archive/master.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "dinbror/blazy", "version": "1.8.2", "type": "drupal-library", "dist": {"url": "https://github.com/dinbror/blazy/archive/refs/heads/master.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "jdnumm/autoheight", "version": "master", "type": "drupal-library", "dist": {"url": "https://github.com/jdnumm/jquery-autoheight/archive/refs/heads/master.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "furf/j<PERSON>y-ui-touch-punch", "version": "master", "type": "drupal-library", "dist": {"url": "https://github.com/furf/jquery-ui-touch-punch/archive/refs/heads/master.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "cure53/dompurify", "version": "2.4.0", "type": "drupal-library", "dist": {"url": "https://github.com/cure53/DOMPurify/archive/refs/tags/2.4.0.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "fengyuanchen/cropper", "version": "v4.1.0", "type": "drupal-library", "dist": {"url": "https://github.com/fengyuanchen/cropper/archive/refs/tags/v4.1.0.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "davidstutz/bootstrap-multiselect", "version": "v1.1.2", "type": "drupal-library", "dist": {"url": "https://github.com/davidstutz/bootstrap-multiselect/archive/refs/tags/v1.1.2.zip", "type": "zip"}}}], "require": {"php": "^8", "drupal/core": "^10", "kenwheeler/slick": "1.8.1", "wikimedia/composer-merge-plugin": "dev-master", "composer/installers": "^1.7", "cweagans/composer-patches": "^1.6", "drupal/core-composer-scaffold": "^10", "drupal/core-project-message": "^10", "drupal/view_mode_selector": "1.x-dev#5d87d17", "drupal/views_role_based_global_text": "^3.1", "drupal/media_entity_browser": "^2.0", "drupal/pdf": "^1.2", "drupal/admin_toolbar": "^3.0.2", "drupal/adminimal_admin_toolbar": "^2.0", "drupal/layout_builder_restrictions": "^2.7", "drupal/bootstrap_layout_builder": "^2", "drupal/poll": "^1.4", "drupal/paragraphs": "^1.12", "drupal/colorbox_load": "^1.2", "jackmoore/colorbox": "^1.6", "drupal/colorbox_inline": "^2.0", "jedfoster/readmorejs": "^2.2", "drupal/ds": "^3.9", "drupal/taxonomy_manager": "^2.0", "owlcarousel2/owlcarousel2": "^2.3", "drupal/commerce_simple_stock": "^1.0", "fullcalendar/fullcalendar": "^5.3", "drupal/schema_metatag": "^2.1", "drupal/image_widget_crop": "^2.3", "fengyuanchen/jquery-cropper": "^1.0", "drupal/quick_node_clone": "^1.13", "vakata/jstree": "^3.3", "drupal/jquery_ui_slider": "^2.1", "drupal/bulk_user_registration": "^2.0", "drupal/auto_height": "^2", "drupal/leaflet": "^10", "drupal/geocoder": "^4.23", "geocoder-php/geo-plugin-provider": "^4.2", "geocoder-php/nominatim-provider": "^5.4", "drupal/sticky": "^2.1", "garand/sticky": "^1.0", "drupal/display_field_copy": "^2.0", "drupal/commerce_cart_flyout": "^1.8", "drupal/commerce_add_to_cart_confirmation": "^1.0@alpha", "drupal/views_conditional": "^1.2", "drupal/time_field": "^2.1", "drupal/conditional_fields": "^4.0@alpha", "drupal/views_infinite_scroll": "^2.0", "drupal/jquery_ui_spinner": "^2.1", "drupal/user_pwreset_timeout": "^2", "drupal/slick_views": "^2.4", "dinbror/blazy": "^1.8", "drupal/libraries": "^4.0", "drupal/jquery_ui_effects": "^2.1", "drupal/multiselect": "^2.0@beta", "drupal/field_formatter_class": "^1.5", "jdnumm/autoheight": "master", "drupal/views_tree": "^2.0@alpha", "drupal/cookies": "^1.1", "drupal/metatag": "^1.22", "drupal/simple_sitemap": "^4.1", "drupal/pathauto": "^1.11", "drupal/advagg": "^6.0@alpha", "drupal/entity_reference_tree": "^2.0", "cure53/dompurify": "^2.4", "fengyuanchen/cropper": "^4.1", "drupal/webform": "^6.2", "drupal/taxonomy_bulk_actions": "^1.0", "drush/drush": "^12.5", "jquery-textfill/jquery-textfill": "^0.6.0", "davatron5000/fittext": "^1.2", "drupal/adminimal_theme": "^1.6", "drupal/security_review": "^2.0", "drupal/captcha": "^1.0", "drupal/recaptcha": "^3.1", "drupal/views_data_export": "^1.2", "drupal/charts": "^5.0", "drupal/tinypng": "^2.0", "drupal/google_analytics": "^4.0", "drupal/google_tag": "^1.6", "drupal/backup_migrate": "^5.0", "drupal/csv_serialization": "^3.0 || ^4.0", "drupal/mimemail": "^1.0@alpha", "drupal/entity_print": "^2.14", "drupal/entity_embed": "^1.6", "drupal/ckeditor": "^1.0", "drupal/calendar": "^1.0@beta", "drupal/better_exposed_filters": "^6.0", "drupal/queue_ui": "^3.1", "drupal/token_filter": "^2.1", "drupal/userprotect": "^1.2", "drupal/twig_field_value": "^2.0", "drupal/video": "^3.0", "drupal/simple_gmap": "^3.1", "drupal/honeypot": "^2.1", "drupal/ultimate_cron": "^2.0@alpha", "drupal/search_api": "^1.27", "drupal/role_delegation": "^1.2", "drupal/restui": "^1.21", "drupal/aristotle": "3.1.0", "drupal/jwt": "^2.1", "drupal/ckeditor_bgimage": "^3.0", "drupal/views_templates": "1.3", "php-http/guzzle7-adapter": "^1.0", "symfony/http-client": "^5.4", "symfony/mime": "^6.4", "symfony/polyfill-php73": "^1.23", "symfony/yaml": "^6.4", "drupal/h5p": "^2.0@alpha", "drupal/popup_field_group": "^1.8", "drupal/field_group": "3.4", "davidstutz/bootstrap-multiselect": "^1.1", "drupal/field_defaults": "^2.0", "drupal/redirect": "^1.10", "pay-now/paynow-php-sdk": "^2.4", "drupal/comment_delete": "^2.0", "drupal/statistics": "^1.0", "drupal/tour": "^2.0", "drupal/views_fieldsets": "^4.0", "drupal/views_sort_expression": "2.0.1", "drupal/config_split": "^2.0", "drupal/devel_mail_logger": "^2.0", "drupal/multiselect_dropdown": "^1.2.4", "drupal/twig_tweak": "^3.4", "drupal/coffee": "^2.0", "drupal/config_log": "4.0", "drupal/tagify": "^1.2", "getresponse/sdk-php": "^3.0"}, "require-dev": {"kint-php/kint": "^5.0", "palantirnet/drupal-rector": "^0.12.4", "phpmd/phpmd": "^2.13", "squizlabs/php_codesniffer": "*", "drupal/production_checklist": "^1.0@alpha", "drupal/devel": "^5.0", "drupal/masquerade": "^2.0@RC"}, "replace": {"h5p/h5p-core": "*", "h5p/h5p-editor": "*"}, "config": {"preferred-install": {"drupal/view_mode_selector": "source", "drupal/views_role_based_global_text": "source", "drupal/calendar": "source", "*": "dist"}, "allow-plugins": {"composer/installers": true, "drupal/console-extend-plugin": true, "cweagans/composer-patches": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true, "wikimedia/composer-merge-plugin": true, "php-http/discovery": true}, "platform-check": false}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"classmap": ["scripts/composer/ScriptHandler.php"]}, "scripts": {"pre-install-cmd": ["DrupalProject\\composer\\ScriptHandler::checkComposerVersion"], "pre-update-cmd": ["DrupalProject\\composer\\ScriptHandler::checkComposerVersion"], "post-install-cmd": ["DrupalProject\\composer\\ScriptHandler::createRequiredFiles"], "post-update-cmd": ["DrupalProject\\composer\\ScriptHandler::createRequiredFiles"]}, "extra": {"installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/{$name}": ["type:drupal-drush"]}, "drupal-scaffold": {"locations": {"web-root": "web/"}}, "enable-patching": true, "composer-exit-on-patch-failure": false, "patchLevel": {"drupal/core": "-p2"}, "patches": {"drupal/core": {"entity reference - new content": "https://www.drupal.org/files/issues/2025-01-02/autocreate-entity-reference-selection-handlers-that-extend-viewsselection-10.4.x.patch"}, "drupal/field_group": {"tabs validation fix": "https://www.drupal.org/files/issues/2023-05-23/field_group_3362124-2.patch", "tabs validation fix 2": "https://www.drupal.org/files/issues/2024-04-10/2969051-124.patch"}, "drupal/bbr": {"D10 patch": "https://www.drupal.org/files/issues/2022-06-15/bbr.2.1.0-rc1.rector.patch"}, "drupal/aristotle": {"removing opigno dependencies": "https://www.drupal.org/files/issues/2024-07-03/3458873-hiding-opigno-dependencies.patch"}, "drupal/tinypng": {"video module conflict fix for yt": "https://www.drupal.org/files/issues/2024-10-09/3479662-video-module-conflict-yt.patch"}}}}