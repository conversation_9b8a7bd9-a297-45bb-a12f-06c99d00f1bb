uuid: null
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.search_index
    - field.storage.node.body
    - field.storage.node.field_company
    - field.storage.node.field_section_01
    - field.storage.node.field_training_category_new
    - field.storage.paragraph.field_text_1
    - field.storage.paragraph.field_text_2
    - search_api.server.default_server
  module:
    - node
    - paragraphs
    - taxonomy
    - user
id: default_index
name: 'Default content index'
description: 'Default content index created by the Database Search Defaults module'
read_only: false
field_settings:
  author:
    label: 'Author name'
    datasource_id: 'entity:node'
    property_path: 'uid:entity:name'
    type: string
    dependencies:
      module:
        - node
        - user
  body:
    label: 'Słowa kluczowe'
    datasource_id: 'entity:node'
    property_path: body
    type: text
    boost: 8.0
    dependencies:
      config:
        - field.storage.node.body
  changed:
    label: Changed
    datasource_id: 'entity:node'
    property_path: changed
    type: date
    dependencies:
      module:
        - node
  company:
    label: 'Organizator szkolenia'
    datasource_id: 'entity:node'
    property_path: 'field_company:entity:title'
    type: text
    boost: 8.0
    dependencies:
      config:
        - field.storage.node.field_company
      module:
        - node
  created:
    label: 'Authored on'
    datasource_id: 'entity:node'
    property_path: created
    type: date
    dependencies:
      module:
        - node
  description_synonyms:
    label: 'Kategoria szkolenia » Termin taksonomii » Opis'
    datasource_id: 'entity:node'
    property_path: 'field_training_category_new:entity:description'
    type: text
    boost: 8.0
    dependencies:
      config:
        - field.storage.node.field_training_category_new
      module:
        - taxonomy
  field_text_1:
    label: 'Tytuł i podtytuł » Akapit » Tytuł'
    datasource_id: 'entity:node'
    property_path: 'field_section_01:entity:field_text_1'
    type: text
    boost: 8.0
    dependencies:
      config:
        - field.storage.node.field_section_01
        - field.storage.paragraph.field_text_1
      module:
        - paragraphs
  field_text_2:
    label: 'Tytuł i podtytuł » Akapit » Podtytuł'
    datasource_id: 'entity:node'
    property_path: 'field_section_01:entity:field_text_2'
    type: text
    boost: 8.0
    dependencies:
      config:
        - field.storage.node.field_section_01
        - field.storage.paragraph.field_text_2
      module:
        - paragraphs
  node_grants:
    label: 'Node access information'
    property_path: search_api_node_grants
    type: string
    indexed_locked: true
    type_locked: true
    hidden: true
  rendered_item:
    label: 'Rendered item'
    property_path: rendered_item
    type: text
    configuration:
      roles:
        - anonymous
      view_mode:
        'entity:group':
          learning_path: teaser_of_group
        'entity:node':
          article: search_index
          forum: search_index
          page: search_index
  status:
    label: 'Publishing status'
    datasource_id: 'entity:node'
    property_path: status
    type: boolean
    indexed_locked: true
    type_locked: true
    dependencies:
      module:
        - node
  sticky:
    label: 'Sticky at top of lists'
    datasource_id: 'entity:node'
    property_path: sticky
    type: boolean
    dependencies:
      module:
        - node
  title:
    label: Title
    datasource_id: 'entity:node'
    property_path: title
    type: text
    boost: 8.0
    dependencies:
      module:
        - node
  type:
    label: 'Content type'
    datasource_id: 'entity:node'
    property_path: type
    type: string
    dependencies:
      module:
        - node
  uid:
    label: 'Author ID'
    datasource_id: 'entity:node'
    property_path: uid
    type: integer
    indexed_locked: true
    type_locked: true
    dependencies:
      module:
        - node
datasource_settings:
  'entity:node':
    bundles:
      default: false
      selected:
        - npx_training
    languages:
      default: true
      selected: {  }
processor_settings:
  add_url:
    weights:
      preprocess_index: -30
  aggregated_field:
    weights:
      add_properties: 20
  content_access:
    weights:
      preprocess_index: -6
      preprocess_query: -4
  custom_value: {  }
  entity_status:
    weights:
      preprocess_index: -10
  entity_type: {  }
  highlight:
    weights:
      postprocess_query: 0
    prefix: '<strong>'
    suffix: '</strong>'
    excerpt: true
    excerpt_always: false
    excerpt_length: 256
    exclude_fields:
      - title
    highlight: always
    highlight_partial: true
  html_filter:
    weights:
      preprocess_index: -3
      preprocess_query: -6
    all_fields: true
    fields:
      - author
      - body
      - company
      - description_synonyms
      - field_text_1
      - field_text_2
      - rendered_item
      - title
      - type
    title: true
    alt: true
    tags:
      b: 2
      h1: 5
      h2: 3
      h3: 2
      string: 2
  ignore_character:
    weights:
      preprocess_index: -10
      preprocess_query: -10
    all_fields: true
    fields:
      - author
      - body
      - company
      - description_synonyms
      - field_text_1
      - field_text_2
      - rendered_item
      - title
      - type
    ignorable: "['¿¡!?,.:;]"
    ignorable_classes:
      - Pc
      - Pd
      - Pe
      - Pf
      - Pi
      - Po
      - Ps
  ignorecase:
    weights:
      preprocess_index: -5
      preprocess_query: -8
    all_fields: true
    fields:
      - author
      - body
      - company
      - description_synonyms
      - field_text_1
      - field_text_2
      - rendered_item
      - title
      - type
  language_with_fallback: {  }
  rendered_item:
    weights:
      add_properties: 0
      pre_index_save: -10
  stopwords:
    weights:
      preprocess_index: -5
      preprocess_query: -10
    all_fields: false
    fields:
      - rendered_item
      - title
    stopwords:
      - a
      - an
      - and
      - are
      - as
      - at
      - be
      - but
      - by
      - for
      - if
      - in
      - into
      - is
      - it
      - 'no'
      - not
      - of
      - 'on'
      - or
      - s
      - such
      - t
      - that
      - the
      - their
      - then
      - there
      - these
      - they
      - this
      - to
      - was
      - will
      - with
  tokenizer:
    weights:
      preprocess_index: -2
      preprocess_query: -5
    all_fields: true
    fields:
      - body
      - company
      - description_synonyms
      - field_text_1
      - field_text_2
      - rendered_item
      - title
    spaces: ''
    ignored: ._-
    overlap_cjk: 1
    minimum_word_size: '3'
  transliteration:
    weights:
      preprocess_index: -4
      preprocess_query: -7
    all_fields: true
    fields:
      - author
      - body
      - company
      - description_synonyms
      - field_text_1
      - field_text_2
      - rendered_item
      - title
      - type
tracker_settings:
  default:
    indexing_order: fifo
options:
  cron_limit: 50
  delete_on_fail: true
  index_directly: true
  track_changes_in_references: true
server: default_server

