uuid: bfd9d67f-4d86-4345-a884-c35d516379b0
langcode: pl
status: true
dependencies:
  config:
    - field.field.paragraph.trainer_with_video.field_trainer_video
    - field.field.paragraph.trainer_with_video.field_trener
    - paragraphs.paragraphs_type.trainer_with_video
  module:
    - field_formatter_class
    - video
id: paragraph.trainer_with_video.default
targetEntityType: paragraph
bundle: trainer_with_video
mode: default
content:
  field_trainer_video:
    type: video_embed_player
    label: above
    settings:
      autoplay: false
      width: '854'
      height: '480'
      related_videos: false
    third_party_settings:
      field_formatter_class:
        class: ''
    weight: 1
    region: content
  field_trener:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  npx_date_cancel_rules: true
  npx_date_confirm_rules: true
  npx_date_mark_rules: true
  npx_date_postpone_rules: true
  search_api_excerpt: true
