uuid: d0d52092-9471-49ef-afae-1f0f1da4cb98
langcode: pl
status: true
dependencies:
  config:
    - field.field.npxnotify_profile.npxnotify_profile.field_disable_notifications
    - field.field.npxnotify_profile.npxnotify_profile.field_send_all_comments
    - field.field.npxnotify_profile.npxnotify_profile.field_subscribed_inquiry
    - field.field.npxnotify_profile.npxnotify_profile.field_training_category
    - field.field.npxnotify_profile.npxnotify_profile.field_training_category_new
  module:
    - entity_reference_tree
    - field_formatter_class
    - field_group
    - npx_notify
third_party_settings:
  field_group:
    group_npx_notify_profile_intro:
      children: {  }
      label: 'npx notify profile intro'
      region: content
      parent_name: ''
      weight: 0
      format_type: html_element
      format_settings:
        classes: ''
        show_empty_fields: true
        id: ''
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
_core:
  default_config_hash: qTyC2sLmVJXH4EbKpHAqvYy1fBcGvo8egf_GFkSaH-s
id: npxnotify_profile.npxnotify_profile.default
targetEntityType: npxnotify_profile
bundle: npxnotify_profile
mode: default
content:
  field_disable_notifications:
    type: boolean_checkbox
    weight: 2
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_training_category_new:
    type: entity_reference_tree
    weight: 1
    region: content
    settings:
      theme: default
      dots: 0
      size: 60
      placeholder: ''
      match_operator: CONTAINS
      match_limit: 10
      dialog_title: 'Kategoria szkolenia'
      label: 'Wybierz kategorie'
    third_party_settings:
      field_formatter_class:
        class: ''
hidden:
  created: true
  field_send_all_comments: true
  field_subscribed_inquiry: true
  field_training_category: true
  label: true
  uid: true
