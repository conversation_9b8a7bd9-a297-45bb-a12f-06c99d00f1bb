uuid: 0ade85ba-2f60-4c23-9cf8-aed89c22356a
langcode: pl
status: true
dependencies:
  config:
    - field.field.taxonomy_term.npx_mailing_categories.field_external_list_id
    - taxonomy.vocabulary.npx_mailing_categories
  module:
    - path
    - text
id: taxonomy_term.npx_mailing_categories.default
targetEntityType: taxonomy_term
bundle: npx_mailing_categories
mode: default
content:
  description:
    type: text_textarea
    weight: 2
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_external_list_id:
    type: string_textfield
    weight: 1
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 3
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  simple_sitemap:
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden: {  }
