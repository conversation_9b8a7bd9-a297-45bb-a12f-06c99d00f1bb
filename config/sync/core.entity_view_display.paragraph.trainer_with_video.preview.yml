uuid: e57e0b50-af5e-415a-b2b8-39280f8a7946
langcode: pl
status: true
dependencies:
  config:
    - core.entity_view_mode.paragraph.preview
    - field.field.paragraph.trainer_with_video.field_trainer_video
    - field.field.paragraph.trainer_with_video.field_trener
    - paragraphs.paragraphs_type.trainer_with_video
  module:
    - field_formatter_class
    - layout_builder
    - video
third_party_settings:
  layout_builder:
    enabled: false
    allow_custom: false
id: paragraph.trainer_with_video.preview
targetEntityType: paragraph
bundle: trainer_with_video
mode: preview
content:
  field_trainer_video:
    type: video_embed_player
    label: hidden
    settings:
      autoplay: false
      width: '314'
      height: '176'
      related_videos: false
    third_party_settings:
      field_formatter_class:
        class: ''
    weight: 1
    region: content
  field_trener:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: teaser2
      link: false
    third_party_settings:
      field_formatter_class:
        class: ''
    weight: 0
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  npx_date_cancel_rules: true
  npx_date_confirm_rules: true
  npx_date_mark_rules: true
  npx_date_postpone_rules: true
  search_api_excerpt: true
