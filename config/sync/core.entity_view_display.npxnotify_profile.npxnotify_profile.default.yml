uuid: 29308cb1-76d6-4099-b33b-0e5555179425
langcode: pl
status: true
dependencies:
  config:
    - field.field.npxnotify_profile.npxnotify_profile.field_disable_notifications
    - field.field.npxnotify_profile.npxnotify_profile.field_send_all_comments
    - field.field.npxnotify_profile.npxnotify_profile.field_subscribed_inquiry
    - field.field.npxnotify_profile.npxnotify_profile.field_training_category
    - field.field.npxnotify_profile.npxnotify_profile.field_training_category_new
  module:
    - npx_notify
    - user
_core:
  default_config_hash: F9UnQjN8KgC33QuA_3hcfs-Ka5ThlhGVJJV1NwFxVOw
id: npxnotify_profile.npxnotify_profile.default
targetEntityType: npxnotify_profile
bundle: npxnotify_profile
mode: default
content:
  created:
    type: timestamp
    label: above
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: long
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 20
    region: content
  field_disable_notifications:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 23
    region: content
  field_training_category_new:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 22
    region: content
  label:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: -5
    region: content
  uid:
    type: author
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 15
    region: content
hidden:
  entity_print_view_epub: true
  entity_print_view_pdf: true
  entity_print_view_word_docx: true
  field_send_all_comments: true
  field_subscribed_inquiry: true
  field_training_category: true
  search_api_excerpt: true
