uuid: 2d6c713c-a5d9-4700-9780-5c60905294ec
langcode: pl
status: true
dependencies:
  config:
    - commerce_product.commerce_product_type.offer
    - field.storage.commerce_product.field_benefit1_days
    - field.storage.commerce_product.field_benefit1_flag
    - field.storage.commerce_product.field_benefit2_days
    - field.storage.commerce_product.field_benefit2_flag
    - field.storage.commerce_product.field_benefit2_minutes
    - field.storage.commerce_product.field_benefit3_days
    - field.storage.commerce_product.field_benefit3_flag
    - field.storage.commerce_product.field_benefit3_minutes
    - field.storage.commerce_product.field_benefit4_days
    - field.storage.commerce_product.field_benefit4_flag
    - field.storage.commerce_product.field_help_field_dont_use_it
    - field.storage.commerce_product.field_offer_accomodation_price
    - field.storage.commerce_product.field_offer_category
    - field.storage.commerce_product.field_offer_catering_price
    - field.storage.commerce_product.field_offer_city
    - field.storage.commerce_product.field_offer_date_end
    - field.storage.commerce_product.field_offer_date_range
    - field.storage.commerce_product.field_offer_days
    - field.storage.commerce_product.field_offer_email
    - field.storage.commerce_product.field_offer_hours
    - field.storage.commerce_product.field_offer_inquiry_counter
    - field.storage.commerce_product.field_offer_max_in_group
    - field.storage.commerce_product.field_offer_message
    - field.storage.commerce_product.field_offer_name
    - field.storage.commerce_product.field_offer_num_of_groups
    - field.storage.commerce_product.field_offer_other_criteria
    - field.storage.commerce_product.field_offer_phone
    - field.storage.commerce_product.field_offer_position
    - field.storage.commerce_product.field_offer_price_netto
    - field.storage.commerce_product.field_offer_region
    - field.storage.commerce_product.field_offer_room_price
    - field.storage.commerce_product.field_offer_service_type
    - field.storage.commerce_product.field_offer_status
    - field.storage.commerce_product.field_offer_trainers_list
    - field.storage.commerce_product.field_offer_training_price
    - field.storage.commerce_product.field_offer_training_type
    - field.storage.commerce_product.field_offer_unit_price_netto
  module:
    - commerce
    - commerce_product
    - datetime
    - datetime_range
    - entity_reference_revisions
    - npx_offer
    - options
    - telephone
    - text
_core:
  default_config_hash: gt5XYTtoZ_eNLl2Up2AMA83JIPDo_tvO5iwNOadFf-g
id: inquiry_offers_list
label: 'Lista ofert'
module: views
description: 'Lista ofert do zapytania ofertowego'
tag: ''
base_table: commerce_product_field_data
base_field: product_id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Ranking ofert'
      fields:
        product_id:
          id: product_id
          table: commerce_product_field_data
          field: product_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_product
          entity_field: product_id
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_help_field_dont_use_it:
          id: field_help_field_dont_use_it
          table: commerce_product__field_help_field_dont_use_it
          field: field_help_field_dont_use_it
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_inquiry_counter:
          id: field_offer_inquiry_counter
          table: commerce_product__field_offer_inquiry_counter
          field: field_offer_inquiry_counter
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: "<span class=\"move-to-prev\" title=\"zamień miejscami z poprzednią pozycją\"></span><span class=\"npx-counter-value\" data-pid=\"{{ product_id }}\">{{ field_offer_inquiry_counter__value }}</span>\r\n<span class=\"move-to-next\" title=\"zamień miejscami z kolejną pozycją\"></span>"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title:
          id: title
          table: commerce_product_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_product
          entity_field: title
          plugin_id: field
          label: 'Rodzaj szkolenia'
          exclude: false
          alter:
            alter_text: true
            text: '<span class="t2b">{{ title }}</span>'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        npx_offer_profile_company_views_field:
          id: npx_offer_profile_company_views_field
          table: views
          field: npx_offer_profile_company_views_field
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: npx_offer_profile_company_views_field
          label: Firma
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
        npx_offer_google_rank_views_field:
          id: npx_offer_google_rank_views_field
          table: views
          field: npx_offer_google_rank_views_field
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: npx_offer_google_rank_views_field
          label: 'Ocena firmy wg Google'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
        field_offer_category:
          id: field_offer_category
          table: commerce_product__field_offer_category
          field: field_offer_category
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Kategoria tematyczna'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_date_end:
          id: field_offer_date_end
          table: commerce_product__field_offer_date_end
          field: field_offer_date_end
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Termin ważności oferty'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: datetime_default
          settings:
            timezone_override: ''
            format_type: certificate_date
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_date_range:
          id: field_offer_date_range
          table: commerce_product__field_offer_date_range
          field: field_offer_date_range
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Zakres terminu realizacji'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: daterange_default
          settings:
            timezone_override: ''
            format_type: datepicker
            from_to: both
            separator: '-'
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_service_type:
          id: field_offer_service_type
          table: commerce_product__field_offer_service_type
          field: field_offer_service_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Rodzaj usługi'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_training_type:
          id: field_offer_training_type
          table: commerce_product__field_offer_training_type
          field: field_offer_training_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Forma usługi'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_days:
          id: field_offer_days
          table: commerce_product__field_offer_days
          field: field_offer_days
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Liczba dni - czas trwania usługi'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_hours:
          id: field_offer_hours
          table: commerce_product__field_offer_hours
          field: field_offer_hours
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Liczba godzin - czas trwania usługi'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_max_in_group:
          id: field_offer_max_in_group
          table: commerce_product__field_offer_max_in_group
          field: field_offer_max_in_group
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Max uczestników w grupie'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_num_of_groups:
          id: field_offer_num_of_groups
          table: commerce_product__field_offer_num_of_groups
          field: field_offer_num_of_groups
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Liczba grup'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit1_days:
          id: field_benefit1_days
          table: commerce_product__field_benefit1_days
          field: field_benefit1_days
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Okres ważności (w dniach)'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit1_flag:
          id: field_benefit1_flag
          table: commerce_product__field_benefit1_flag
          field: field_benefit1_flag
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Wsparcie trenera – mailowe'
          exclude: false
          alter:
            alter_text: true
            text: "{% if field_benefit1_flag__value == 1 %}\r\n{% if field_benefit1_days != \"\" %}\r\n<span class=\"list-yes\"></span>\r\nważne {{ field_benefit1_days }} dni\r\n{% else %}\r\n<span class=\"list-yes\"></span>\r\nważne bez limitu\r\n{% endif %}\r\n{% else %}<span class=\"list-no\"></span>{% endif %}"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: unicode-yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit2_minutes:
          id: field_benefit2_minutes
          table: commerce_product__field_benefit2_minutes
          field: field_benefit2_minutes
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Trwający (w min.)'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit2_days:
          id: field_benefit2_days
          table: commerce_product__field_benefit2_days
          field: field_benefit2_days
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Okres ważności (w dniach)'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit2_flag:
          id: field_benefit2_flag
          table: commerce_product__field_benefit2_flag
          field: field_benefit2_flag
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Wsparcie trenera – telefoniczne'
          exclude: false
          alter:
            alter_text: true
            text: "{% if field_benefit2_flag__value == 1 %}\r\n{% if field_benefit2_days != \"\" %}\r\n<div class=\"d-block\">\r\n<span class=\"list-yes\"></span>\r\nważne {{ field_benefit2_days }} dni\r\n</div>\r\n{% else %}\r\n<div class=\"d-block\">\r\n<span class=\"list-yes\"></span>\r\nważne: bez limitu\r\n</div>\r\n{% endif %}\r\n{% if field_benefit2_minutes != \"\" %}\r\n<div class=\"d-block\">\r\n<span class=\"list-yes\"></span>\r\ntrwające {{ field_benefit2_minutes }} min.\r\n</div>\r\n{% else %}\r\n<div class=\"d-block\">\r\n<span class=\"list-yes\"></span>\r\ntrwające: bez limitu\r\n</div>\r\n{% endif %}\r\n{% else %}<span class=\"list-no\"></span>{% endif %}"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: unicode-yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit3_minutes:
          id: field_benefit3_minutes
          table: commerce_product__field_benefit3_minutes
          field: field_benefit3_minutes
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Trwający (w min.)'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit3_days:
          id: field_benefit3_days
          table: commerce_product__field_benefit3_days
          field: field_benefit3_days
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Okres ważności (w dniach)'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit3_flag:
          id: field_benefit3_flag
          table: commerce_product__field_benefit3_flag
          field: field_benefit3_flag
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Trening indywidualny'
          exclude: false
          alter:
            alter_text: true
            text: "{% if field_benefit3_flag__value == 1 %}\r\n{% if field_benefit3_days != \"\" %}\r\n<div class=\"d-block\">\r\n<span class=\"list-yes\"></span>\r\nważny {{ field_benefit3_days }} dni\r\n</div>\r\n{% else %}\r\n<div class=\"d-block\">\r\n<span class=\"list-yes\"></span>\r\nważny: bez limitu\r\n</div>\r\n{% endif %}\r\n{% if field_benefit3_minutes != \"\" %}\r\n<div class=\"d-block\">\r\n<span class=\"list-yes\"></span>\r\ntrwający {{ field_benefit3_minutes }} min.\r\n</div>\r\n{% else %}\r\n<div class=\"d-block\">\r\n<span class=\"list-yes\"></span>\r\ntrwający: bez limitu\r\n</div>\r\n{% endif %}\r\n{% else %}\r\n<span class=\"list-no\"></span>{% endif %}"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: unicode-yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit4_days:
          id: field_benefit4_days
          table: commerce_product__field_benefit4_days
          field: field_benefit4_days
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Okres ważności (w dniach)'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_benefit4_flag:
          id: field_benefit4_flag
          table: commerce_product__field_benefit4_flag
          field: field_benefit4_flag
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'E-learning po szkoleniu'
          exclude: false
          alter:
            alter_text: true
            text: "{% if field_benefit4_flag__value == 1 %}\r\n{% if field_benefit4_days != \"\" %}\r\nważny {{ field_benefit4_days }} dni\r\n{% else %}\r\nważny: bez limitu\r\n{% endif %}<br/>\r\n{% else %}✖{% endif %}"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: unicode-yes-no
            format_custom_false: ''
            format_custom_true: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_other_criteria:
          id: field_offer_other_criteria
          table: commerce_product__field_offer_other_criteria
          field: field_offer_other_criteria
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'W cenie usługi zawarte są:'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_region:
          id: field_offer_region
          table: commerce_product__field_offer_region
          field: field_offer_region
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Lokalizacja
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_city:
          id: field_offer_city
          table: commerce_product__field_offer_city
          field: field_offer_city
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Miasto
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_name:
          id: field_offer_name
          table: commerce_product__field_offer_name
          field: field_offer_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Imię i nazwisko'
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_position:
          id: field_offer_position
          table: commerce_product__field_offer_position
          field: field_offer_position
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Stanowisko
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_email:
          id: field_offer_email
          table: commerce_product__field_offer_email
          field: field_offer_email
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Email
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: email_mailto
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_phone:
          id: field_offer_phone
          table: commerce_product__field_offer_phone
          field: field_offer_phone
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Telefon
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: telephone_link
          settings:
            title: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_training_price:
          id: field_offer_training_price
          table: commerce_product__field_offer_training_price
          field: field_offer_training_price
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Cena usługi rozwojowej'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_decimal
          settings:
            thousand_separator: ''
            decimal_separator: ','
            scale: 2
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_room_price:
          id: field_offer_room_price
          table: commerce_product__field_offer_room_price
          field: field_offer_room_price
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Cena Sali'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_decimal
          settings:
            thousand_separator: ''
            decimal_separator: ','
            scale: 2
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_accomodation_price:
          id: field_offer_accomodation_price
          table: commerce_product__field_offer_accomodation_price
          field: field_offer_accomodation_price
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Cena noclegów'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_decimal
          settings:
            thousand_separator: ''
            decimal_separator: ','
            scale: 2
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_catering_price:
          id: field_offer_catering_price
          table: commerce_product__field_offer_catering_price
          field: field_offer_catering_price
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Cena wyżywienia'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_decimal
          settings:
            thousand_separator: ''
            decimal_separator: ','
            scale: 2
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_unit_price_netto:
          id: field_offer_unit_price_netto
          table: commerce_product__field_offer_unit_price_netto
          field: field_offer_unit_price_netto
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Ogólna cena za osobę netto'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_decimal
          settings:
            thousand_separator: ''
            decimal_separator: ','
            scale: 2
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_price_netto:
          id: field_offer_price_netto
          table: commerce_product__field_offer_price_netto
          field: field_offer_price_netto
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Cena ogólna'
          exclude: false
          alter:
            alter_text: false
            text: '{{ field_offer_price_netto }} '
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_decimal
          settings:
            thousand_separator: ''
            decimal_separator: ','
            scale: 2
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_trainers_list:
          id: field_offer_trainers_list
          table: commerce_product__field_offer_trainers_list
          field: field_offer_trainers_list
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Trener 1'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_revisions_entity_view
          settings:
            view_mode: preview
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ' '
          field_api_classes: false
        npx_product_offer_reviews_views_field:
          id: npx_product_offer_reviews_views_field
          table: views
          field: npx_product_offer_reviews_views_field
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: npx_product_offer_reviews_views_field
          label: 'Opinia 1'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
        field_offer_message:
          id: field_offer_message
          table: commerce_product__field_offer_message
          field: field_offer_message
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: Wiadomość
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_message_1:
          id: field_offer_message_1
          table: commerce_product__field_offer_message
          field: field_offer_message
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Wiadomość od Partnera'
          exclude: false
          alter:
            alter_text: true
            text: "<div class=\"t2 text-tertiary\">\r\n{{ field_offer_message_1 }}\r\n{% if field_offer_message|length > field_offer_message_1|length %}\r\n  <a class=\"colorbox-inline d-block text-center\" href=\"#full-content-{{ product_id }}\">więcej</a>\r\n  <div class=\"d-none\">\r\n    <div id=\"full-content-{{ product_id }}\">\r\n      {{ field_offer_message }}\r\n    </div>\r\n  </div>\r\n{% endif %}\r\n</div>"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_trimmed
          settings:
            trim_length: 200
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_offer_status:
          id: field_offer_status
          table: commerce_product__field_offer_status
          field: field_offer_status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Status oferty'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: list_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        offer_comment_edit:
          id: offer_comment_edit
          table: views
          field: offer_comment_edit
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: offer_comment_edit
          label: 'Własny komentarz'
          exclude: false
          alter:
            alter_text: false
            text: "Np.: Ocena składowych:\r\n<ul>\r\n<li>0-5: cena</li>\r\n<li>0-5: program/pomysł</li>\r\n<li>0-5: doświadczenie/know-how</li>\r\n<li>0-5: trenerzy</li>\r\n<li>0-5: benefity</li>\r\n<li>0-5: opinie, referencje, oceny</li>\r\n<li>0-5: etapy projektu</li>\r\n</ul>"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        npx_offer_actions_views_field:
          id: npx_offer_actions_views_field
          table: views
          field: npx_offer_actions_views_field
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: npx_offer_actions_views_field
          label: 'Wybierz status oferty'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
        nothing_1:
          id: nothing_1
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: custom
          label: 'Osoba kontaktowa'
          exclude: false
          alter:
            alter_text: true
            text: "{{ field_offer_name }}<br/>\r\n{{ field_offer_position }} <br/>\r\n{{ field_offer_email }} <br/>\r\n{{ field_offer_phone }}"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 0
      exposed_form:
        type: basic
        options:
          submit_button: Zastosuj
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: '<p>Obecnie brak ofert</p>'
          tokenize: false
      sorts:
        field_offer_inquiry_counter_value:
          id: field_offer_inquiry_counter_value
          table: commerce_product__field_offer_inquiry_counter
          field: field_offer_inquiry_counter_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      arguments:
        product_id:
          id: product_id
          table: commerce_product_field_data
          field: product_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_product
          entity_field: product_id
          plugin_id: numeric
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: product
          default_argument_options: {  }
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: true
          not: false
      filters:
        type:
          id: type
          table: commerce_product_field_data
          field: type
          entity_type: commerce_product
          entity_field: type
          plugin_id: commerce_entity_bundle
          value:
            offer: offer
          expose:
            operator_limit_selection: false
            operator_list: {  }
        status:
          id: status
          table: commerce_product_field_data
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_product
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        npx_offer_withdrawn_field_filter:
          id: npx_offer_withdrawn_field_filter
          table: commerce_product
          field: npx_offer_withdrawn_field_filter
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: commerce_product
          plugin_id: npx_offer_withdrawn_field_filter
          operator: '='
          value: '0'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: npx_offer_withdrawn_field_filter_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: npx_offer_withdrawn_field_filter
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              statistics_reader: '0'
              user_manager: '0'
              content_manager: '0'
              anonymous: '0'
              content_editor: '0'
              administrator: '0'
              partner: '0'
              trusted_partner: '0'
              hr: '0'
              business_partner: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: true
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      css_class: 'npx-admin-table n-loading n-loading-full'
      use_ajax: false
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: " {{ drupal_block('npx_inquiry_progress_block') }} "
            format: full_html
          tokenize: true
        area_1:
          id: area_1
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: "{{ drupal_block('npx_inquiry_title_block') }}"
            format: full_html
          tokenize: true
      footer: {  }
      display_extenders:
        metatag_display_extender:
          metatags: {  }
          tokenize: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
      tags:
        - 'config:field.storage.commerce_product.field_benefit1_days'
        - 'config:field.storage.commerce_product.field_benefit1_flag'
        - 'config:field.storage.commerce_product.field_benefit2_days'
        - 'config:field.storage.commerce_product.field_benefit2_flag'
        - 'config:field.storage.commerce_product.field_benefit2_minutes'
        - 'config:field.storage.commerce_product.field_benefit3_days'
        - 'config:field.storage.commerce_product.field_benefit3_flag'
        - 'config:field.storage.commerce_product.field_benefit3_minutes'
        - 'config:field.storage.commerce_product.field_benefit4_days'
        - 'config:field.storage.commerce_product.field_benefit4_flag'
        - 'config:field.storage.commerce_product.field_help_field_dont_use_it'
        - 'config:field.storage.commerce_product.field_offer_accomodation_price'
        - 'config:field.storage.commerce_product.field_offer_category'
        - 'config:field.storage.commerce_product.field_offer_catering_price'
        - 'config:field.storage.commerce_product.field_offer_city'
        - 'config:field.storage.commerce_product.field_offer_date_end'
        - 'config:field.storage.commerce_product.field_offer_date_range'
        - 'config:field.storage.commerce_product.field_offer_days'
        - 'config:field.storage.commerce_product.field_offer_email'
        - 'config:field.storage.commerce_product.field_offer_hours'
        - 'config:field.storage.commerce_product.field_offer_inquiry_counter'
        - 'config:field.storage.commerce_product.field_offer_max_in_group'
        - 'config:field.storage.commerce_product.field_offer_message'
        - 'config:field.storage.commerce_product.field_offer_name'
        - 'config:field.storage.commerce_product.field_offer_num_of_groups'
        - 'config:field.storage.commerce_product.field_offer_other_criteria'
        - 'config:field.storage.commerce_product.field_offer_phone'
        - 'config:field.storage.commerce_product.field_offer_position'
        - 'config:field.storage.commerce_product.field_offer_price_netto'
        - 'config:field.storage.commerce_product.field_offer_region'
        - 'config:field.storage.commerce_product.field_offer_room_price'
        - 'config:field.storage.commerce_product.field_offer_service_type'
        - 'config:field.storage.commerce_product.field_offer_status'
        - 'config:field.storage.commerce_product.field_offer_trainers_list'
        - 'config:field.storage.commerce_product.field_offer_training_price'
        - 'config:field.storage.commerce_product.field_offer_training_type'
        - 'config:field.storage.commerce_product.field_offer_unit_price_netto'
